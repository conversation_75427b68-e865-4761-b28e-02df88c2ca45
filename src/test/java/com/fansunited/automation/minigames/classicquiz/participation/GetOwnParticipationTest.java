package com.fansunited.automation.minigames.classicquiz.participation;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.CreateQuizEndpoint.createQuiz;
import static com.fansunited.automation.core.apis.minigames.classicquiz.GetOwnParticipation.getQuizParticipation;
import static com.fansunited.automation.core.apis.minigames.classicquiz.ParticipateQuizEndpoint.participateQuiz;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItems;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.model.CommonStatus;
import io.restassured.http.ContentType;
import java.util.ArrayList;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@Execution(ExecutionMode.SAME_THREAD)
public class GetOwnParticipationTest extends MiniGamesApiBaseTest {

  @Test
  @DisplayName("Get own participation happy path")
  public void getOwnParticipation() throws HttpException, InterruptedException {
    int quizCount = 3;
    var quizIds = new ArrayList<String>();

    // Create multiple quizzes
    for (int i = 0; i < quizCount; i++) {
      var quizResponse =
          createQuiz(
              classicQuizRequest(CommonStatus.ACTIVE),
              CLIENT_AUTOMATION_ID,
              AuthConstants.ENDPOINTS_API_KEY,
              ContentType.JSON,
              FANS_UNITED_CLIENTS,
              null);

      quizResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

      String quizId =
          quizResponse.path("data.id"); // Assuming the ID is retrieved from the response
      quizIds.add(quizId); // Store quiz ID for later retrieval

      // Participate in each quiz
      try {
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 1),
            quizId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
      } catch (HttpException e) {
        throw new RuntimeException(e);
      }
    }


    var getQuizResponse =
        getQuizParticipation(
            quizIds.get(0) + "," + quizIds.get(1) + "," + quizIds.get(2),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    getQuizResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.size()", equalTo(quizCount))
        .body("data.classic_quiz_id", hasItems(quizIds.get(0), quizIds.get(1), quizIds.get(2)));
  }
}
