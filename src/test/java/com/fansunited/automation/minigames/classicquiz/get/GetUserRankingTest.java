package com.fansunited.automation.minigames.classicquiz.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.GetQuizRankEndpoint.getQuizUserRank;
import static com.fansunited.automation.core.apis.minigames.classicquiz.ParticipateQuizEndpoint.participateQuiz;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.hasItem;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@Execution(ExecutionMode.SAME_THREAD)
public class GetUserRankingTest extends MiniGamesApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify get classic quiz user rank works correctly and the user is in different position in various quizzes")
  public void getClassicQuizUserRankTest() throws IllegalArgumentException, HttpException,
      IOException, ExecutionException, FirebaseAuthException, InterruptedException {

    var quizIds = createQuizzes(3);
    var userId=createUsers(2);
    var participationInFirstQuiz =
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 1),
            quizIds.get(0),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationInFirstQuiz.then().assertThat().statusCode(200);

    var participationInSecondQuiz =
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 2),
            quizIds.get(1),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationInSecondQuiz.then().assertThat().statusCode(200);

    var otherUserParticipateInSecondQuiz =
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 1),
            quizIds.get(1),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
           userId.get(0).getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    otherUserParticipateInSecondQuiz.then().assertThat().statusCode(200);

    var participationFirstUserResponse =
        participateQuiz(
            participateClassicQuizRequest(1, 1, 2, 2),
            quizIds.get(2),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationFirstUserResponse.then().assertThat().statusCode(200);

    var participationSecondUserResponse =
        participateQuiz(
            participateClassicQuizRequest(1, 2, 2, 2),
            quizIds.get(0),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    participationSecondUserResponse.then().assertThat().statusCode(200);

    var userRankingResponse =
        getQuizUserRank(
            getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    userRankingResponse.then().assertThat().statusCode(200)

        .body("data.findAll { it.classic_quiz_id == '" + quizIds.get(1) + "' }.points", hasItem(2))
        .body("data.findAll { it.classic_quiz_id == '" + quizIds.get(1) + "' }.position", hasItem(2))
        .body("data.findAll { it.classic_quiz_id == '" + quizIds.get(0) + "' }.points", hasItem(4))
        .body("data.findAll { it.classic_quiz_id == '" + quizIds.get(0) + "' }.position", hasItem(1))
        .body("data.findAll { it.classic_quiz_id == '" + quizIds.get(2) + "' }.points", hasItem(2))
        .body("data.findAll { it.classic_quiz_id == '" + quizIds.get(2) + "' }.position", hasItem(1));
  }
}
