package com.fansunited.automation.minigames.classicquiz.participation;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.INVALID_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.GetOwnParticipation.getQuizParticipation;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.oneOf;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@Execution(ExecutionMode.SAME_THREAD)
public class GetOwnParticipationValidationTest extends MiniGamesApiBaseTest {


  @ParameterizedTest(name = "Verify can not get participation with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getParticipationWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws IllegalArgumentException, HttpException {

    var getQuizResponse =
        getQuizParticipation(
            "quizId",
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            String.valueOf(argumentsHolder),
            ContentType.JSON);

    getQuizResponse
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when get own participation with invalid client id Action: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getQuizByIdWithInvalidClientId(String clientId)
      throws HttpException {

    var response =
        getQuizParticipation(
            "quizId",
            clientId,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response,
        oneOf(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }


  @ParameterizedTest(name = "Verify can not get nonexisting quiz {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = INVALID_ID)
  @NullAndEmptySource
  public void getOwnParticipationWithWrongQuizId (String quizId)
      throws IllegalArgumentException, HttpException {

    var response =
        getQuizParticipation(
            quizId,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    switch (response.statusCode()) {
      case HttpStatus.SC_OK:
        response.then().assertThat().body("data", empty());
        break;
      case HttpStatus.SC_BAD_REQUEST:
        response.then().assertThat().statusCode(HttpStatus.SC_BAD_REQUEST);
        break;
      default:
        throw new AssertionError("Unexpected status code: " + response.statusCode());
    }
  }  



}
