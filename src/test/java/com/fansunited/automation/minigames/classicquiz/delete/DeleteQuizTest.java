package com.fansunited.automation.minigames.classicquiz.delete;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.classicquiz.DeleteQuizEndpoint.deleteQuiz;
import static com.fansunited.automation.core.apis.minigames.classicquiz.GetQuizById.getQuizById;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.model.CommonStatus.INACTIVE;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Delete quiz happy path")
@Execution(ExecutionMode.SAME_THREAD)
public class DeleteQuizTest extends MiniGamesApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify delete classic quiz works")
  public void deleteClassicQuizTest() throws IllegalArgumentException, HttpException {

    var quizId = createQuizForTest(INACTIVE).getData().getId();

    var response = deleteQuiz(quizId, CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var getDeletedQuiz = getQuizById(quizId, CLIENT_AUTOMATION_ID,
        FANS_UNITED_CLIENTS, null, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    getDeletedQuiz
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_NOT_FOUND);
  }
}
