package com.fansunited.automation.minigames.eitheror.participation;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.eitherOr.ParticipateInTriviaGame.participateInEitherOr;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrWinningCondition;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EmptySource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@Execution(ExecutionMode.SAME_THREAD)
public class ParticipateTriviaGameValidationTest extends MiniGamesApiBaseTest {

  @ParameterizedTest
  @DisplayName("Can not participate when either/or id is:{arguments}")
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_ID)
  @EmptySource
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void participateWhenEitherOrIdIsInvalid(String id) throws HttpException {

    var response =
        participateInEitherOr(
            eitherOrParticipation("1", true, "1_2", 3F),
            id,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    response.then().assertThat().statusCode(HttpStatus.SC_NOT_FOUND);
  }

  @ParameterizedTest
  @DisplayName("Can not participate when client id is:{arguments}")
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_ID)
  @NullAndEmptySource
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void canNotParticipateWhenClientIdIsInvalid(String client_id) throws HttpException {

    var id = createTriviaGameForTests(EitherOrWinningCondition.MORE).getData().getId();
    var response =
        participateInEitherOr(
            eitherOrParticipation("1", true, "1_2", 3F),
            id,
            client_id,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    response.then().assertThat().statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @DisplayName("Participate trivia game with not specified content type")
  @Tag(REGRESSION)
  public void updateEitherOrWithWrongContentTypeTest()
      throws IllegalArgumentException,
      HttpException {
    
    var id = createTriviaGameForTests(EitherOrWinningCondition.MORE).getData().getId();

    var response =
        participateInEitherOr(
            eitherOrParticipation("1", true, "1_2", 3F),
            id,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            null);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo("invalid_content_type"));
  }


  @ParameterizedTest(
      name = "Verify can not participate trivia game with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void participateEitherOrWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var id = createTriviaGameForTests(EitherOrWinningCondition.MORE).getData().getId();

    var response =
        participateInEitherOr(
            eitherOrParticipation("1", true, "1_2", 3F),
            id,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            String.valueOf(argumentsHolder),
            ContentType.JSON);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }
  @Test
  @DisplayName("Participate trivia game when answer is not found")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void participateEitherOrHappyPath() throws HttpException {

    var id = createTriviaGameForTests(EitherOrWinningCondition.MORE).getData().getId();

    var response =
        participateInEitherOr(
            eitherOrParticipation("test", true, "test", 2F),
            id,
            CLIENT_AUTOMATION_ID,
            FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(),
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);
    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body("error.message", equalTo("Answer not found"));
  }

}
