package com.fansunited.automation.minigames.eitheror.post;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.minigames.eitherOr.CreateEitherOrEndpoint.createEitherOr;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.oneOf;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.base.minigames.MiniGamesApiBaseTest;
import com.fansunited.automation.model.CommonStatus;
import com.fansunited.automation.model.loyaltyapi.activity.request.Campaign;
import com.fansunited.automation.model.loyaltyapi.activity.request.Content;
import com.fansunited.automation.model.loyaltyapi.activity.request.Context;
import com.fansunited.automation.model.minigamesapi.classicquiz.GameImagesDto;
import com.fansunited.automation.model.minigamesapi.classicquiz.ImagesDto;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrOption;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrPoint;
import com.fansunited.automation.model.minigamesapi.eitheror.EitherOrWinningCondition;
import com.fansunited.automation.model.minigamesapi.eitheror.request.EitherOrRequest;
import com.fansunited.automation.validators.ErrorValidator;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import java.io.IOException;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Create either or validation tests")
@Execution(ExecutionMode.SAME_THREAD)
public class CreateEitherOrValidationTest extends MiniGamesApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Trivia game can not be created with user token")
  public void createTriviaGameWithUserToken()
      throws IllegalArgumentException,
          IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException,
          HttpException {

    var user = createUser();
    var createTriviaGameResponse =
        createEitherOr(
            updateTriviaGameRequest(EitherOrWinningCondition.MORE,CommonStatus.ACTIVE),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_PROFILE,
            user.getEmail());

    createTriviaGameResponse.then().assertThat().statusCode(HttpStatus.SC_FORBIDDEN);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Trivia game can not be created without wining condition")
  public void createTriviaGameWithoutWiningCondition()
      throws IllegalArgumentException, HttpException {

    var createTriviaGameResponse =
        createEitherOr(
            updateTriviaGameRequest(null,CommonStatus.ACTIVE),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    createTriviaGameResponse
        .then()
        .log()
        .ifValidationFails()
        .assertThat()
        .statusCode(HttpStatus.SC_BAD_REQUEST)
        .body(
            "error.message",
            equalTo("winningCondition: The field is required"));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Trivia game can not be created if content type is NOT specified")
  public void createTriviaGameWithoutContentType() throws IllegalArgumentException, HttpException {

    var createTriviaGameResponse =
        createEitherOr(
            updateTriviaGameRequest(EitherOrWinningCondition.MORE,CommonStatus.ACTIVE),
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            null,
            FANS_UNITED_CLIENTS,
            null);

    createTriviaGameResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo("invalid_content_type"));
  }

  @ParameterizedTest(
      name =
          "Verify API returns BAD_REQUEST when creating trivia game for client with action filter. Action: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_ID)
  @NullAndEmptySource
  public void createQuizWithInvalidClientId(String clientId) throws HttpException {

    var response =
        createEitherOr(
            updateTriviaGameRequest(EitherOrWinningCondition.MORE,CommonStatus.ACTIVE),
            clientId,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(
        response, oneOf(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN));
  }

  @ParameterizedTest(
      name = "Verify can not create trivia game with invalid/missing api key. Api key: {arguments}")
  @Tag(REGRESSION)
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void createEitherOrWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        createEitherOr(
            updateTriviaGameRequest(EitherOrWinningCondition.MORE,CommonStatus.ACTIVE),
            CLIENT_AUTOMATION_ID,
            String.valueOf(argumentsHolder),
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    currentTestResponse.set(response);

    response.then().assertThat().statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify create Either/Or works when options are less then 5")
  public void createTriviaGameWithLessOptionsTest() throws IllegalArgumentException, HttpException {

    var tags =
        List.of(
            com.fansunited.automation.model.loyaltyapi.activity.request.Tag.builder()
                .source(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
                .type(ApiConstants.ProfileApi.Interest.FOOTBALL.getSource())
                .id(TEAM_ID_LIVERPOOL)
                .build());

    var contentId = UUID.randomUUID().toString();
    var campaignId = UUID.randomUUID().toString();

    var content = new Content();
    content.setId(contentId);
    content.setType(new Faker().howIMetYourMother().quote());
    content.setLabel(new Faker().howIMetYourMother().quote());

    var campaign = new Campaign();
    campaign.setId(campaignId);
    campaign.setLabel("Carlsberg 2022");

    var context = new Context();
    context.setContent(content);
    context.setTags(tags);
    context.setCampaign(campaign);

    var createRequest =
        EitherOrRequest.builder()
            .title("Only for test")
            .description("Just testing")
            .flags(List.of("flag1", "flag2"))
            .images(ImagesDto.builder().main("test.jpg").mobile("test.jpg").cover("test").build())
            .status(String.valueOf(CommonStatus.ACTIVE))
            .winning_condition(EitherOrWinningCondition.MORE)
            .context(context)
            .points(
                (List.of(
                    EitherOrPoint.builder().correct_steps(0).score(5).build(),
                    EitherOrPoint.builder().correct_steps(5).score(10).build(),
                    EitherOrPoint.builder().correct_steps(10).score(20).build())))
            .options(
                List.of(
                    (EitherOrOption.builder()
                        .id("1")
                        .label("label1")
                        .images(GameImagesDto.builder().main("soso.jpg").mobile("sad.jpg").build())
                        .value(1)
                        .build()),
                    (EitherOrOption.builder()
                        .id("2")
                        .label("label2")
                        .value(2)
                        .images(GameImagesDto.builder().main("happy.jpg").mobile("sad.jpg").build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("3")
                        .value(3)
                        .label("label3")
                        .images(GameImagesDto.builder().main("happy.jpg").mobile("sad.jpg").build())
                        .build()),
                    (EitherOrOption.builder()
                        .id("4")
                        .value(5)
                        .label("label4")
                        .images(GameImagesDto.builder().main("happy.jpg").mobile("sad.jpg").build())
                        .build())))
            .lives(3)
            .rules("no bugs")
            .time(3)
            .build();

    var createTriviaGameResponse =
        createEitherOr(
            createRequest,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS,
            null);

    createTriviaGameResponse.then().assertThat().statusCode(HttpStatus.SC_BAD_REQUEST);
  }
}
