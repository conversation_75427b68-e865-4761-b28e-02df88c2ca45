package com.fansunited.automation.loyaltyapi.ranks;

import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.DEFAULT_PAGE_LIMIT;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.POINTS_PROP;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.POSITION_PROP;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.PREDICTIONS_MADE_PROP;
import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.PROFILE_ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.UPDATED_AT_PROP;
import static com.fansunited.automation.constants.JsonSchemasPath.LoyaltyApi.Endpoints.Ranks.GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.loyaltyapi.LeaderboardByIdEndpoint.getLeaderboardForTemplateWithIdNoCache;
import static com.fansunited.automation.core.apis.predictionapi.GameEndpoint.updateGame;
import static com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint.createPredictionForUser;
import static com.fansunited.automation.core.base.AuthBase.createUser;
import static com.fansunited.automation.core.base.AuthBase.createUsers;
import static com.fansunited.automation.core.base.resolver.ResolverBase.cleanUpMatchIdList;
import static com.fansunited.automation.core.base.resolver.ResolverBase.init;
import static com.fansunited.automation.core.resolver.Resolver.updateMatchToAnotherStatus;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static com.fansunited.automation.helpers.FirebaseHelper.ARCHIVE_LEADERBOARD_COLLECTION;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE;
import static com.fansunited.automation.helpers.FirebaseHelper.GAME_COLLECTION;
import static com.fansunited.automation.helpers.FirebaseHelper.getEntityFromFirebaseCollection;
import static com.fansunited.automation.helpers.FirebaseHelper.updateCollectionField;
import static com.fansunited.automation.helpers.FirebaseHelper.updateCollectionTimestampField;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;

import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.GameLeaderboardsEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.enums.GoldenGoalRankingOrderTestsInput;
import com.fansunited.automation.core.apis.loyaltyapi.enums.PredictionsInLeaderboardTestInput;
import com.fansunited.automation.core.apis.loyaltyapi.enums.TimeRankingOrderTestsInput;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.PredictionsEndpoint;
import com.fansunited.automation.core.base.loyaltyapi.LoyaltyApiBaseTest;
import com.fansunited.automation.core.base.resolver.ResolverBase;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.core.watchers.TestWatcherImpl;
import com.fansunited.automation.helpers.CustomHamcrestMatchers;
import com.fansunited.automation.helpers.DateTripletHelper;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.helpers.synchelper.TestSynchronizationHelper;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateGroupFilter;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateGroups;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.model.predictionapi.games.enums.GameStatus;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CorrectScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.request.UpdateGameRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.validators.CacheValidator;
import com.fansunited.automation.validators.LoyaltyApiValidator;
import com.fansunited.automation.validators.RedisValidator;
import com.github.javafaker.Faker;
import com.google.cloud.Timestamp;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@DisplayName("Loyalty Api - GET /v1/leaderboard/games/{templateId} endpoint happy path tests")
public class GetLeaderboardForGameTests extends LoyaltyApiBaseTest {

  private static final Logger LOG = LoggerFactory.getLogger(GetLeaderboardForGameTests.class);

  @AfterAll
  public static void cleanUp() {
    TestWatcherImpl.setShouldTruncateTables(true);
    LOG.info("All postconditions completed for the test suite.");
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Get rankings for game with specific id")
  public void getRankingsGameWithSpecificId()
      throws Exception {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);
    Resolver.openMatchesForPredictions(matchList);
    var gameId = GamesEndpoint.createGamesWithMatchList(GameType.TOP_X, 1, matchList).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE, GameType.TOP_X, 30, gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 40,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(2).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 0,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()), 1, "1",
        predictionLastUpdate);

    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.SINGLE, 30,
        null,
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(4).getMatchId()), 1, "1",
        predictionLastUpdate);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    GameLeaderboardsEndpoint gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    var getLeaderboardResponse =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(3))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[2]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-1015")})
  @DisplayName("Verify pagination for specific game type leaderboard works")
  public void getRankingsForGameTemplatePagination() throws Exception {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);
    Resolver.openMatchesForPredictions(matchList);
    var gameId = GamesEndpoint.createGamesWithMatchList(GameType.TOP_X, 1, matchList).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));
    int numberOfEvents = 16;

    for (int i = 0; i < numberOfEvents; i++) {
      InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), UUID.randomUUID().toString(),
          PredictionMarket.CORRECT_SCORE,
          GameType.TOP_X, generateRandomNumber(0, 300),
          gameInstance.getId(),
          MatchByIdEndpoint.getMatchDtoById(matchList
              .get(generateRandomNumber(0, matchList.size() - 1))
              .getId()), 1, "1", predictionLastUpdate);
    }

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    GameLeaderboardsEndpoint gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    var getLeaderboardResponse =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(DEFAULT_PAGE_LIMIT))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("meta.pagination.current_page", equalTo(1))
        .body("meta.pagination.number_of_pages",
            equalTo((int) Math.ceil((double) numberOfEvents / DEFAULT_PAGE_LIMIT)))
        .body("meta.pagination.total_items", equalTo(numberOfEvents));

     gameLeaderboardsEndpoint.setGameId(gameInstance.getId());
     gameLeaderboardsEndpoint.setPage(2);

    gameLeaderboardsEndpoint.getLeaderboardForGameId()
        .then()
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(numberOfEvents - DEFAULT_PAGE_LIMIT))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("meta.pagination.current_page", equalTo(2))
        .body("meta.pagination.items_per_page", equalTo(DEFAULT_PAGE_LIMIT));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/leaderboard/games/{gameId} response returned by the server is cached for 1h")
  public void verifyGetLeaderboardForGameIdIsCached() throws HttpException {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);
    Resolver.openMatchesForPredictions(matchList);
    var gameId = GamesEndpoint.createGamesWithMatchList(GameType.TOP_X, 1, matchList).get(0);

    GameLeaderboardsEndpoint gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameId).build();

    var response =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateCacheExpirationDate(response, CacheValidator.CachePeriod.TWO_HOURS);
  }

  @ParameterizedTest(name = "Ranking with even score is based on golden goal tie-breaker with arguments: {arguments} ")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(GoldenGoalRankingOrderTestsInput.class)
  public void getRankingsWhereGoldenGoalIsTieBreaker(GoldenGoalRankingOrderTestsInput goldenGoalRankingOrderTestsInput
      ) throws Exception {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);
    Resolver.openMatchesForPredictions(matchList);

    var gameId = GamesEndpoint.createGamesWithMatchList(GameType.TOP_X, 1, matchList).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = "a-" + UUID.randomUUID();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, goldenGoalRankingOrderTestsInput.getPointsPlayer1(),
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        goldenGoalRankingOrderTestsInput.getGoldenGoalPredictionPlayer1(),
        goldenGoalRankingOrderTestsInput.getFirstGoal(), predictionLastUpdate);

    var profileIdSecondPlace = "b-" + UUID.randomUUID();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, goldenGoalRankingOrderTestsInput.getPointsPlayer2(),
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(2).getMatchId()),
        goldenGoalRankingOrderTestsInput.getGoldenGoalPredictionPlayer2(),
        goldenGoalRankingOrderTestsInput.getFirstGoal(), predictionLastUpdate);

    var profileIdThirdPlace = "c-" + UUID.randomUUID();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, goldenGoalRankingOrderTestsInput.getPointsPlayer3(),
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()),
        goldenGoalRankingOrderTestsInput.getGoldenGoalPredictionPlayer3(),
        goldenGoalRankingOrderTestsInput.getFirstGoal(), predictionLastUpdate);

    TestSynchronizationHelper.getInstance().completePreconditionAndAddMidCondition(
        TestSynchronizationHelper.USER_RANKING_GAME_MV, gameInstance.getId());

    GameLeaderboardsEndpoint gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    var getLeaderboardResponse =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    List<Integer> leaderboardPositionsOrder =
        goldenGoalRankingOrderTestsInput.getLeaderboardPositionsOrder();

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .log().ifValidationFails()
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(3))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + POSITION_PROP, equalTo(leaderboardPositionsOrder.get(0)))
        .body("data[1]." + POSITION_PROP, equalTo(leaderboardPositionsOrder.get(1)))
        .body("data[2]." + POSITION_PROP, equalTo(leaderboardPositionsOrder.get(2)))
        .body("data.profile_id", containsInAnyOrder(profileIdFirstPlace, profileIdSecondPlace, profileIdThirdPlace));

  }

  @ParameterizedTest(name = "Ranking for Match Quiz is correctly sorted relative to time tie-breaker with arguments: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = TimeRankingOrderTestsInput.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_ABBREVIATION_MQ)
  public void getRankingsForMatchQuizWhereTimeIsTieBreaker(
      TimeRankingOrderTestsInput timeRankingInputs)
      throws Exception {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);
    Resolver.openMatchesForPredictions(matchList);

    var gameId = GamesEndpoint.createGamesWithMatchList(GameType.TOP_X, 1, matchList).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    var profileIdFirstPlace = "a-" + UUID.randomUUID();
    InsertBigQData.insertSingleRankEvent(
        timeRankingInputs.getInitialTime()
            .plusSeconds(timeRankingInputs.getPlusSecondsFromNowPlayer1()),
        profileIdFirstPlace, PredictionMarket.CORRECT_SCORE, GameType.MATCH_QUIZ,
        timeRankingInputs.getPointsPlayer1(), gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        timeRankingInputs.getGoldenGoalPredictionPlayer1(), timeRankingInputs.getFirstGoal(),
        timeRankingInputs.getLastUpdatedTimePlayer1());

    var profileIdSecondPlace = "b-" + UUID.randomUUID();
    InsertBigQData.insertSingleRankEvent(
        timeRankingInputs.getInitialTime()
            .plusSeconds(timeRankingInputs.getPlusSecondsFromNowPlayer2()),
        profileIdSecondPlace, PredictionMarket.CORRECT_SCORE, GameType.MATCH_QUIZ,
        timeRankingInputs.getPointsPlayer2(), gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        timeRankingInputs.getGoldenGoalPredictionPlayer2(), timeRankingInputs.getFirstGoal(),
        timeRankingInputs.getLastUpdatedTimePlayer2());

    var profileIdThirdPlace = "c-" + UUID.randomUUID();

    InsertBigQData.insertSingleRankEvent(
        timeRankingInputs.getInitialTime()
            .plusSeconds(timeRankingInputs.getPlusSecondsFromNowPlayer3()),
        profileIdThirdPlace, PredictionMarket.CORRECT_SCORE, GameType.MATCH_QUIZ,
        timeRankingInputs.getPointsPlayer3(), gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        timeRankingInputs.getGoldenGoalPredictionPlayer3(), timeRankingInputs.getFirstGoal(),
        timeRankingInputs.getLastUpdatedTimePlayer3());

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    GameLeaderboardsEndpoint gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    var getLeaderboardResponse = gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    List<Integer> leaderboardPositionsOrder = timeRankingInputs.getLeaderboardPositionsOrder();

    LOG.error(String.format("response of %s/%s", gameInstance.getId(), getLeaderboardResponse.prettyPrint()));
    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .log().ifValidationFails()
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(3))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + POSITION_PROP, equalTo(leaderboardPositionsOrder.get(0)))
        .body("data[1]." + POSITION_PROP, equalTo(leaderboardPositionsOrder.get(1)))
        .body("data[2]." + POSITION_PROP, equalTo(leaderboardPositionsOrder.get(2)))
        .body("data.profile_id", containsInAnyOrder(profileIdFirstPlace, profileIdSecondPlace, profileIdThirdPlace));
  }

  @ParameterizedTest(name = "Ranking for Top X is correctly sorted relative to time tie-breaker with arguments: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @EnumSource(value = TimeRankingOrderTestsInput.class, mode = EnumSource.Mode.MATCH_ANY, names = RegexConstants.CONTAINS_ABBREVIATION_TX)
  public void getRankingsForTopXWhereTimeIsTieBreaker(TimeRankingOrderTestsInput timeRankingInputs)
      throws Exception {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);
    Resolver.openMatchesForPredictions(matchList);

    var gameId = GamesEndpoint.createGamesWithMatchList(GameType.TOP_X, 1, matchList).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    var profileIdFirstPlace = "a-" + UUID.randomUUID();
    InsertBigQData.insertSingleRankEvent(
        timeRankingInputs.getInitialTime().plusSeconds(timeRankingInputs.getPlusSecondsFromNowPlayer1()),
        profileIdFirstPlace, PredictionMarket.CORRECT_SCORE, GameType.TOP_X,
        timeRankingInputs.getPointsPlayer1(), gameInstance.getId(), MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        timeRankingInputs.getGoldenGoalPredictionPlayer1(), timeRankingInputs.getFirstGoal(), timeRankingInputs.getLastUpdatedTimePlayer1());

    var profileIdSecondPlace = "b-" + UUID.randomUUID();
    InsertBigQData.insertSingleRankEvent(
        timeRankingInputs.getInitialTime().plusSeconds(timeRankingInputs.getPlusSecondsFromNowPlayer2()),
        profileIdSecondPlace, PredictionMarket.CORRECT_SCORE, GameType.TOP_X,
        timeRankingInputs.getPointsPlayer2(), gameInstance.getId(), MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        timeRankingInputs.getGoldenGoalPredictionPlayer2(), timeRankingInputs.getFirstGoal(), timeRankingInputs.getLastUpdatedTimePlayer2());

    var profileIdThirdPlace = "c-" + UUID.randomUUID();
    InsertBigQData.insertSingleRankEvent(
        timeRankingInputs.getInitialTime().plusSeconds(timeRankingInputs.getPlusSecondsFromNowPlayer3()),
        profileIdThirdPlace, PredictionMarket.CORRECT_SCORE, GameType.TOP_X,
        timeRankingInputs.getPointsPlayer3(), gameInstance.getId(), MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        timeRankingInputs.getGoldenGoalPredictionPlayer3(), timeRankingInputs.getFirstGoal(), timeRankingInputs.getLastUpdatedTimePlayer3());

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    GameLeaderboardsEndpoint gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    var getLeaderboardResponse =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    List<Integer> leaderboardPositionsOrder = timeRankingInputs.getLeaderboardPositionsOrder();

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .log().ifValidationFails()
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(3))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + POSITION_PROP, equalTo(leaderboardPositionsOrder.get(0)))
        .body("data[1]." + POSITION_PROP, equalTo(leaderboardPositionsOrder.get(1)))
        .body("data[2]." + POSITION_PROP, equalTo(leaderboardPositionsOrder.get(2)))
        .body("data.profile_id", containsInAnyOrder(profileIdFirstPlace, profileIdSecondPlace, profileIdThirdPlace));
  }

  @Test
  @DisplayName("Profile Ids are sorted in asc order when getting Leaderboard rankings")
  public void profileIdsOrderInRankingsGameWithSpecificId()
      throws Exception {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);
    Resolver.openMatchesForPredictions(matchList);
    var gameId = GamesEndpoint.createGamesWithMatchList(GameType.TOP_X, 1, matchList).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(2).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdFourthPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFourthPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdFifthPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFifthPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdSixthPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSixthPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdSeventhPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSeventhPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdEightPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdEightPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdNinthPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdNinthPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()), 1, "1",
        predictionLastUpdate);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    GameLeaderboardsEndpoint gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    var getLeaderboardResponse =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(9))
        .body("data.profile_id",
            containsInAnyOrder(profileIdFirstPlace, profileIdSecondPlace, profileIdThirdPlace,
                profileIdFourthPlace, profileIdFifthPlace, profileIdSixthPlace,
                profileIdSeventhPlace, profileIdEightPlace, profileIdNinthPlace));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Rankings for game does not include predictions made for postponed matches")
  public void getRankingsGameWithPostponedMatch()
      throws Exception {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    matchList.forEach(match -> {
      match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
      match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
    });

    Resolver.openMatchesForPredictions(matchList);
    init();
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(matchList.stream().map(Match::getId).toList(), GameType.TOP_X,
                GameStatus.OPEN, matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    UserRecord user = createUser();

    var profileIdFirstPlace = user.getUid();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()),
        90, "1", predictionLastUpdate);

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 40,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(2).getMatchId()),
        90, "1", predictionLastUpdate);

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 300,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()),
        90, "1", predictionLastUpdate);

    RedisValidator.validateGameExistsInActiveGames(gameInstance.getId());

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(match -> predictionFixtures.add(CorrectScorePredictionFixture.builder()
        .matchId(match.getId())
        .matchType(MatchType.FOOTBALL.getValue())
        .goalsHome(match.getGoalsFullTimeHome())
        .goalsAway(match.getGoalsFullTimeAway())
        .build()));

    createPredictionForUser(CreatePredictionRequest.builder()
        .fixtures(predictionFixtures)
        .gameInstanceId(gameInstance.getId())
        .build(), user.getEmail());

    //Postpone the match
    int indexForFixtureWithPostponedMatch = 0;

    updateMatchToAnotherStatus(
        gameInstance.getFixtures().get(indexForFixtureWithPostponedMatch).getMatchId(),
        MatchGenerator.STATUS_POSTPONED);

    //Remove the postponed match from the list of matches which will be set to 'finished' status
    matchList.remove(indexForFixtureWithPostponedMatch);

    Resolver.updateMatchesToBeFinishedInThePast(matchList, 50);

    Resolver.resolve(matchList.size());

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    GameLeaderboardsEndpoint gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    var getLeaderboardResponse =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .log().ifValidationFails()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data", hasSize(3))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace))
        .body("data[2]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[1]." + POINTS_PROP, equalTo(280));

    updateMatchToAnotherStatus(
        gameInstance.getFixtures().get(indexForFixtureWithPostponedMatch).getMatchId(),
        MatchGenerator.STATUS_NOT_STARTED);
  }

  //todo: Fails with different results each time
  @Disabled("WIP FZ-1624")
  @ParameterizedTest(name = "Prediction count is added to leaderboard and sort order is correct with params as = {arguments}")
  @EnumSource(PredictionsInLeaderboardTestInput.class)
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("Ticket FZ-1624")})
  public void verifyPredictionCountIsAddedToLeaderboards(PredictionsInLeaderboardTestInput testInput)
      throws IOException, ExecutionException, FirebaseAuthException, InterruptedException,
      HttpException {

    //Setup matches outcomes
    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);

    var matchHomeWin = matchList.get(0);
    matchHomeWin.setGoalsFullTimeHome((byte) 2);
    matchHomeWin.setGoalsFullTimeAway((byte) 0);

    var matchAwayWin = matchList.get(1);
    matchAwayWin.setGoalsFullTimeHome((byte) 1);
    matchAwayWin.setGoalsFullTimeAway((byte) 3);

    var matchDrawWin = matchList.get(2);
    matchDrawWin.setGoalsFullTimeHome((byte) 4);
    matchDrawWin.setGoalsFullTimeAway((byte) 4);

    var matchPostponed = matchList.get(3);
    matchPostponed.setGoalsFullTimeHome((byte) 0);
    matchPostponed.setGoalsFullTimeAway((byte) 0);

    Resolver.openMatchesForPredictions(matchList);
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());
    //End set up matches---------------<

    var dateTriplet = DateTripletHelper.getInstance().getNextNegative();
    var fromDateString = dateTriplet.getFromString();
    var toDateString = dateTriplet.getToString();
    //Create a Template containing the three matches
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().name().title())
        .fromDate(fromDateString)
        .toDate(toDateString)
        .markets(List.of(PredictionMarket.CORRECT_SCORE.getValue()))
        .matchIds(matchList.stream().map(Match::getId).toList())
        .build();

    var response = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(response);

    LoyaltyApiValidator.validateTemplateResponse(response, templateRequest);

    var templateResponse = response.as(TemplateResponse.class);
    //End create template---------------<

    //Create users
    List<UserRecord> userRecordList = Helper.sortUsersByIdInAscendingOrder(createUsers(3));

    //Setup and create predictions requests for each match and user
    List<CreatePredictionRequest> predictionRequestList = setUpPredictionsRequests(matchList);
    createPredictionsForTest(testInput, predictionRequestList, userRecordList);

    matchList.forEach(
        match ->
            Resolver.updateMatchKickOffAndFinishDates(
                match.getId(),
                dateTriplet.getMidLocalDateTime(),
                dateTriplet.getMidLocalDateTime(),
                MatchGenerator.STATUS_FINISHED));

    //Postpone the match
    updateMatchToAnotherStatus(
        matchPostponed.getId(), MatchGenerator.STATUS_POSTPONED);

    Resolver.resolve(matchList.size());

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_TEMPLATE_MV,
            templateResponse.getId());

    var getLeaderboardResponse = getLeaderboardForTemplateWithIdNoCache(templateResponse.getId(), CLIENT_AUTOMATION_ID, 1, 100);

    currentTestResponse.set(getLeaderboardResponse);

    //Assertion for profile_id might fail when profiles have same position, because the api returns them randomly
    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(
            JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_LEADERBOARD_FOR_TEMPLATE_SCHEMA))
        .body("data." + POSITION_PROP, CustomHamcrestMatchers.IsInAscendingNumericalOrder())
        .body("data." + POINTS_PROP, CustomHamcrestMatchers.IsInDescendingNumericalOrder())
        .body("data[0]." + POSITION_PROP, equalTo(testInput.getPositionUserOne()))
        .body("data[0]." + POINTS_PROP, equalTo(testInput.getPointsUserOne()))
        .body("data[0]." + PREDICTIONS_MADE_PROP, equalTo(testInput.getPredictionsMadeUserOne()))
        .body("data[1]." + POSITION_PROP, equalTo(testInput.getPositionUserTwo()))
        .body("data[1]." + POINTS_PROP, equalTo(testInput.getPointsUserTwo()))
        .body("data[1]." + PREDICTIONS_MADE_PROP, equalTo(testInput.getPredictionsMadeUserTwo()))
        .body("data[2]." + POSITION_PROP, equalTo(testInput.getPositionUserThree()))
        .body("data[2]." + POINTS_PROP, equalTo(testInput.getPointsUserThree()))
        .body("data[2]." + PREDICTIONS_MADE_PROP, equalTo(testInput.getPredictionsMadeUserThree()));
  }

  private void createPredictionsForTest(PredictionsInLeaderboardTestInput typeOfPredictions,
      List<CreatePredictionRequest> createPredictionRequestList,
      List<UserRecord> users) throws HttpException {

    //Profile 1
    if(typeOfPredictions == PredictionsInLeaderboardTestInput.U1_100pt_4pr_1pos_U2_50pt_4pr_2pos_U3_0pt_4pr_3pos ||
       typeOfPredictions == PredictionsInLeaderboardTestInput.U1_100pt_4pr_1pos_U2_50pt_4pr_2pos_U3_50pt_4pr_2pos ||
       typeOfPredictions == PredictionsInLeaderboardTestInput.U1_100pt_4pr_1pos_U2_50pt_3pr_2pos_U3_50pt_4pr_3pos) {
      //Predictions userOne
      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(0),
          users.get(0).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(2),
          users.get(0).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(5),
          users.get(0).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(6),
          users.get(0).getEmail());
    }

    if(typeOfPredictions == PredictionsInLeaderboardTestInput.U1_100pt_4pr_1pos_U2_50pt_4pr_2pos_U3_0pt_4pr_3pos ||
        typeOfPredictions == PredictionsInLeaderboardTestInput.U1_100pt_4pr_1pos_U2_50pt_4pr_2pos_U3_50pt_4pr_2pos) {
      //Predictions userTwo
      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(0),
          users.get(1).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(3),
          users.get(1).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(5),
          users.get(1).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(6),
          users.get(1).getEmail());
    }

    if(typeOfPredictions == PredictionsInLeaderboardTestInput.U1_100pt_4pr_1pos_U2_50pt_3pr_2pos_U3_50pt_4pr_3pos) {
      //Predictions userTwo
      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(0),
          users.get(1).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(5),
          users.get(1).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(6),
          users.get(1).getEmail());
    }

    if(typeOfPredictions == PredictionsInLeaderboardTestInput.U1_100pt_4pr_1pos_U2_50pt_4pr_2pos_U3_0pt_4pr_3pos) {
      //Predictions userThree
      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(1),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(3),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(5),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(7),
          users.get(2).getEmail());
    }

    if(typeOfPredictions == PredictionsInLeaderboardTestInput.U1_100pt_4pr_1pos_U2_50pt_4pr_2pos_U3_50pt_4pr_2pos) {
      //Predictions userThree
      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(1),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(2),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(5),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(7),
          users.get(2).getEmail());

    }

    if(typeOfPredictions == PredictionsInLeaderboardTestInput.U1_100pt_4pr_1pos_U2_50pt_3pr_2pos_U3_50pt_4pr_3pos) {
      //Predictions userThree
      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(1),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(2),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(5),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(7),
          users.get(2).getEmail());
    }

    if(typeOfPredictions == PredictionsInLeaderboardTestInput.U1_0pt_4pr_1pos_U2_0pt_4pr_1pos_U3_0pt_4pr_1pos) {
      //Predictions userOne
      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(1),
          users.get(0).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(3),
          users.get(0).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(5),
          users.get(0).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(7),
          users.get(0).getEmail());
      //Predictions userTwo
      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(1),
          users.get(1).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(3),
          users.get(1).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(5),
          users.get(1).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(7),
          users.get(1).getEmail());
      //Predictions userThree
      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(1),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(3),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(5),
          users.get(2).getEmail());

      PredictionsEndpoint.createPredictionForUser(createPredictionRequestList.get(7),
          users.get(2).getEmail());
    }
  }

  private List<CreatePredictionRequest> setUpPredictionsRequests(List<Match> matchList){

    List<CreatePredictionRequest> predictionRequestList = new ArrayList<>();

    var createCorrectPredictionRequestForHomeWinMatch =
        CreatePredictionRequest.builder().fixtures(List.of(
            CorrectScorePredictionFixture.builder()
                .matchId(matchList.get(0).getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(2)
                .goalsAway(0)
                .build())).build();
    predictionRequestList.add(createCorrectPredictionRequestForHomeWinMatch);

    var createIncorrectPredictionRequestForHomeWinMatch =
        CreatePredictionRequest.builder().fixtures(List.of(
            CorrectScorePredictionFixture.builder()
                .matchId(matchList.get(0).getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(0)
                .goalsAway(1)
                .build())).build();
    predictionRequestList.add(createIncorrectPredictionRequestForHomeWinMatch);

    var createCorrectPredictionRequestForAwayWinMatch =
        CreatePredictionRequest.builder().fixtures(List.of(
            CorrectScorePredictionFixture.builder()
                .matchId(matchList.get(1).getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(1)
                .goalsAway(3)
                .build())).build();
    predictionRequestList.add(createCorrectPredictionRequestForAwayWinMatch);

    var createIncorrectPredictionRequestForAwayWinMatch =
        CreatePredictionRequest.builder().fixtures(List.of(
            CorrectScorePredictionFixture.builder()
                .matchId(matchList.get(1).getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(3)
                .goalsAway(0)
                .build())).build();
    predictionRequestList.add(createIncorrectPredictionRequestForAwayWinMatch);

    var createCorrectPredictionRequestForDrawMatch =
        CreatePredictionRequest.builder().fixtures(List.of(
            CorrectScorePredictionFixture.builder()
                .matchId(matchList.get(2).getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(4)
                .goalsAway(4)
                .build())).build();
    predictionRequestList.add(createCorrectPredictionRequestForDrawMatch);

    var createIncorrectPredictionRequestForDrawMatch =
        CreatePredictionRequest.builder().fixtures(List.of(
            CorrectScorePredictionFixture.builder()
                .matchId(matchList.get(2).getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(1)
                .goalsAway(2)
                .build())).build();
    predictionRequestList.add(createIncorrectPredictionRequestForDrawMatch);

    var createCorrectPredictionRequestForPostponedMatch =
        CreatePredictionRequest.builder().fixtures(List.of(
            CorrectScorePredictionFixture.builder()
                .matchId(matchList.get(3).getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(0)
                .goalsAway(0)
                .build())).build();
    predictionRequestList.add(createCorrectPredictionRequestForPostponedMatch);

    var createIncorrectPredictionRequestForPostponedMatch =
        CreatePredictionRequest.builder().fixtures(List.of(
            CorrectScorePredictionFixture.builder()
                .matchId(matchList.get(3).getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(3)
                .goalsAway(2)
                .build())).build();
    predictionRequestList.add(createIncorrectPredictionRequestForPostponedMatch);

    return predictionRequestList;
  }

  // -20
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1943")})
  @DisplayName("Verify leaderboard for game is archived when game settled")
  public void verifyGameArchivedAfterSettle()
      throws HttpException, InterruptedException, IOException, ExecutionException,
      FirebaseAuthException {

    var dateTriplet = DateTripletHelper.getInstance().getNextNegative();
    //Create Game
    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);

    matchList.forEach(match -> {
      match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
      match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
    });

    Resolver.openMatchesForPredictions(matchList);
    ResolverBase.init();
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var response =
        GamesEndpoint.createGame(matchList.stream().map(Match::getId).toList(), GameType.TOP_X,
                GameStatus.OPEN, matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1));
    var gameInstance = response.as(GameInstance.class);

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(match -> predictionFixtures.add(CorrectScorePredictionFixture.builder()
        .matchId(match.getId())
        .matchType(MatchType.FOOTBALL.getValue())
        .goalsHome(match.getGoalsFullTimeHome())
        .goalsAway(match.getGoalsFullTimeAway())
        .build()));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .gameInstanceId(gameInstance.getId()).fixtures(predictionFixtures).build();

    PredictionsEndpoint.createPredictionForUser(createPredictionRequest, createUser().getEmail());

    // Important is to have the match moved to the past
    matchList.forEach(
        match ->
            Resolver.updateMatchKickOffAndFinishDates(
                match.getId(),
                dateTriplet.getMidLocalDateTime(),
                dateTriplet.getMidLocalDateTime(),
                MatchGenerator.STATUS_FINISHED));

    Thread.sleep(10000);
    //Close the game in order to be able to move to Settled Status
    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION), gameInstance.getId(),
        STATUS_PROP, GameStatus.CLOSED.getValue());

    Resolver.resolve(matchList.size());

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION), gameInstance.getId(),
        STATUS_PROP, GameStatus.SETTLED.getValue());

    updateCollectionTimestampField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION), gameInstance.getId(),
        UPDATED_AT_PROP, Timestamp.parseTimestamp(dateTriplet.getToLocalDateTime().toString()));

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV_NO_WAIT,
            "dummyid");

    waitForArchiveToBeFilled(gameInstance.getId());

    GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build().getLeaderboardForGameId();

    var expectedArchivedTemplateId = gameInstance.getId();
    var actualDocument = getEntityFromFirebaseCollection(FANS_UNITED_PROFILE, ARCHIVE_LEADERBOARD_COLLECTION,
        expectedArchivedTemplateId, "id");
    var realId = actualDocument.get("id").toString();
    Assertions.assertEquals(expectedArchivedTemplateId, realId);
  }

  // -22
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1574"), @Tag("FZ-3475")})
  @DisplayName("Get rankings for game when user has been excluded from the game")
  public void verifyLeaderboardByGameIdWhenUserExcludedFromGame()
      throws Exception {

    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);
    Resolver.openMatchesForPredictions(matchList);
    var gameId = GamesEndpoint.createGamesWithMatchList(GameType.TOP_X, 1, matchList).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);
    var predictionLastUpdate = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ISO8601_WITH_NANO));

    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1",
        predictionLastUpdate);
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(1).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdSecondPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdSecondPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 40,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(2).getMatchId()), 1, "1",
        predictionLastUpdate);

    var profileIdThirdPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 0,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(3).getMatchId()), 1, "1",
        predictionLastUpdate);

    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdThirdPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.SINGLE, 30,
        null,
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(4).getMatchId()), 1, "1",
        predictionLastUpdate);

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV,
            gameInstance.getId());

    //Get Leaderboard by game id before profileIdFirstPlace to be excluded from the game
    GameLeaderboardsEndpoint gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    var getLeaderboardResponse =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(3))
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdFirstPlace));

    //Update the game by excluding profileIdFirstPlace
    var updatedGameRequest = UpdateGameRequest.builder()
        .excludedProfileIds(List.of(profileIdFirstPlace))
        .status("OPEN")
        .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    updateGameResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("excluded_profile_ids", contains(profileIdFirstPlace));

    //Get Leaderboard by game id after profileIdFirstPlace to be excluded from the game
    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV_NO_WAIT,
            gameInstance.getId());
    waitForGameToBeReady(gameInstance.getId());

    GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build().getLeaderboardForGameId().then()
        .log().all()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(2))
        .body("data[0]." + PROFILE_ID_PROP, equalTo(profileIdSecondPlace))
        .body("data[1]." + PROFILE_ID_PROP, equalTo(profileIdThirdPlace));
  }

  // -22 -20 days
  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1574")})
  @DisplayName("Verify user removed from archived game")
  public void verifyUserExcludedFromArchivedGame()
      throws HttpException, InterruptedException, IOException, ExecutionException,
      FirebaseAuthException {

    UserRecord user = createUser();

    //Create Game
    var dateTriplet = DateTripletHelper.getInstance().getNextNegative();
    var endDate = dateTriplet.getToLocalDateTime();
    var matchList = MatchGenerator.generateMatchesInFuture(6, 6);

    matchList.forEach(match -> {
      match.setGoalsFullTimeHome((byte) Helper.generateRandomNumber(1, 4));
      match.setGoalsFullTimeAway((byte) Helper.generateRandomNumber(1, 4));
    });

    matchList.forEach(m -> m.setFinishedAt(dateTriplet.getMidLocalDateTime()));
    Resolver.openMatchesForPredictions(matchList);
    ResolverBase.init();
    cleanUpMatchIdList.addAll(matchList.stream().map(Match::getId).toList());

    var gameInstance =
        GamesEndpoint.createGame(matchList.stream().map(Match::getId).toList(), GameType.TOP_X,
                GameStatus.OPEN, matchList.get(0).getKickoffAt().atZone(ZoneId.of("UTC")).minusHours(1))
            .as(GameInstance.class);

    var predictionFixtures = new ArrayList<PredictionFixture>();

    matchList.forEach(match -> predictionFixtures.add(CorrectScorePredictionFixture.builder()
        .matchId(match.getId())
        .matchType(MatchType.FOOTBALL.getValue())
        .goalsHome(match.getGoalsFullTimeHome())
        .goalsAway(match.getGoalsFullTimeAway() + 1)
        .build()));

    var createPredictionRequest = CreatePredictionRequest.builder()
        .gameInstanceId(gameInstance.getId()).fixtures(predictionFixtures).build();

    PredictionsEndpoint.createPredictionForUser(createPredictionRequest, user.getEmail());

    // Important is to have the match moved to the past
    matchList.forEach(
        match ->
            Resolver.updateMatchKickOffAndFinishDates(
                match.getId(),
                dateTriplet.getMidLocalDateTime(),
                dateTriplet.getMidLocalDateTime(),
                MatchGenerator.STATUS_FINISHED));

    Thread.sleep(10000);
    //Close the game in order to be able to move to Settled Status
    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION), gameInstance.getId(),
        STATUS_PROP, GameStatus.CLOSED.getValue());

    Resolver.resolve(matchList.size());

    updateCollectionField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION), gameInstance.getId(),
        STATUS_PROP, GameStatus.SETTLED.getValue());
    //update Game updated_at field in Firestore
    updateCollectionTimestampField(
        FirebaseHelper.getFirestoreCollection(FANS_UNITED_PROFILE, GAME_COLLECTION), gameInstance.getId(),
        UPDATED_AT_PROP, Timestamp.parseTimestamp(String.valueOf(endDate)));


    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV_NO_WAIT,
            "dummyid");

    waitForArchiveToBeFilled(gameInstance.getId());

    //By getting the Leaderboard by Game Id, an Archive is created in Firebase and the game is stored there
    //Get Leaderboard by game id before profileIdFirstPlace to be excluded from the game
    GameLeaderboardsEndpoint gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    var getLeaderboardResponse =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .log().ifValidationFails()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(1))
        .body("data[0]." + PROFILE_ID_PROP, equalTo(user.getUid()));

    var expectedArchivedTemplateId = gameInstance.getId();
    var actualDocument = getEntityFromFirebaseCollection(FANS_UNITED_PROFILE, ARCHIVE_LEADERBOARD_COLLECTION, expectedArchivedTemplateId,
        "id");
    var realId = actualDocument.get("id").toString();

    Assertions.assertEquals(expectedArchivedTemplateId, realId);

    //WA for update game failure
    var gameInstanceLatest = GameEndpoint.getGameById(gameInstance.getId()).as(GameInstance.class);

    // TODO: this is an issue - we need to set status in order to update game
    var updatedGameRequest = UpdateGameRequest.builder()
        .excludedProfileIds(List.of(user.getUid()))
        .status(gameInstanceLatest.getStatus())
        .build();

    var updateGameResponse = updateGame(gameInstance.getId(), updatedGameRequest);

    currentTestResponse.set(updateGameResponse);

    updateGameResponse
        .then().log().all()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("excluded_profile_ids", contains(user.getUid()));

    TestSynchronizationHelper.getInstance()
        .completePreconditionAndAddMidCondition(TestSynchronizationHelper.USER_RANKING_GAME_MV_NO_WAIT,
            "dummyid");

    waitForArchiveToBeDumped(gameInstance.getId());

    gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
        .gameId(gameInstance.getId()).build();

    getLeaderboardResponse =
        gameLeaderboardsEndpoint.getLeaderboardForGameId();

    currentTestResponse.set(getLeaderboardResponse);

    getLeaderboardResponse
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data", hasSize(0));
  }


  private void waitForArchiveToBeFilled(String id)
      throws IOException, ExecutionException, InterruptedException, HttpException {
    for (int i = 0; i < 30; i++) {
      FirebaseHelper.deleteDocument(FANS_UNITED_PROFILE, ARCHIVE_LEADERBOARD_COLLECTION, id);

      var gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
          .gameId(id).build();

      var getLeaderboardResponse =
          gameLeaderboardsEndpoint.getLeaderboardForGameId();

      Response response = getLeaderboardResponse
          .then()
          .extract()
          .response();

      List<?> data = response.jsonPath().getList("data");
      if (data != null && !data.isEmpty()) {
        FirebaseHelper.deleteDocument(FANS_UNITED_PROFILE, ARCHIVE_LEADERBOARD_COLLECTION, id);
        LOG.info("Archive {} was created successfully", id);
        return;
      }
      Thread.sleep(10 * 1000);
    }
    LOG.error("Archive {} was NOT created successfully", id);
  }

  private void waitForArchiveToBeDumped(String id)
      throws IOException, ExecutionException, InterruptedException, HttpException {
    for (int i = 0; i < 30; i++) {
      FirebaseHelper.deleteDocument(FANS_UNITED_PROFILE, ARCHIVE_LEADERBOARD_COLLECTION, id);

      var gameLeaderboardsEndpoint = GameLeaderboardsEndpoint.builder()
          .gameId(id).build();

      var getLeaderboardResponse =
          gameLeaderboardsEndpoint.getLeaderboardForGameId();

      Response response = getLeaderboardResponse
          .then()
          .extract()
          .response();

      List<?> data = response.jsonPath().getList("data");
      if (data != null && data.isEmpty()) {
        FirebaseHelper.deleteDocument(FANS_UNITED_PROFILE, ARCHIVE_LEADERBOARD_COLLECTION, id);
        LOG.info("Archive {} was dumped successfully", id);
        return;
      }
      Thread.sleep(10 * 1000);
    }
    LOG.error("Archive {} was NOT dumped successfully", id);
  }

  private void waitForGameToBeReady(String id) throws InterruptedException, HttpException {
    for (int i = 0; i < 30; i++) {
      var response = GameLeaderboardsEndpoint.builder()
          .gameId(id).build().getLeaderboardForGameId().then()
          .extract()
          .response();

      List<?> data = response.jsonPath().getList("data");
      if (data != null && data.size() == 2) {
        LOG.info("Game {} is ready", id);
        return;
      }
      Thread.sleep(10 * 1000);
    }
    LOG.error("Game {} was NOT ready on time", id);
  }
}
