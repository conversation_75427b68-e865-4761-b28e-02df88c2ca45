package com.fansunited.automation.loyaltyapi.statistics;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.loyaltyapi.StatisticsTopRatesEndpoint;
import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Loyalty Api - GET /v1/statistics/top endpoint validation tests")
public class GetStatisticsTopValidationTests extends BaseTest {

  @ParameterizedTest(name = "Verify statistics top rates cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getStatisticsTopRateWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        StatisticsTopRatesEndpoint.getStatisticsTopRates(CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(), ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting own statistics with invalid client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getStatisticsTopRatesWithInvalidClientId(String clientId)
      throws HttpException {

    var response =
        StatisticsTopRatesEndpoint.getStatisticsTopRates(clientId, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify statistics top can be fetched with JWT token and the status is OK")
  public void getStatisticsTopRatesWithJwtToken() throws HttpException {

    var response =
        StatisticsTopRatesEndpoint.getStatisticsTop(CLIENT_AUTOMATION_ID, null,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, true, null,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting own statistics top rates with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION)})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getStatisticsTopRatesWithNonSupportedContentType(ContentType contentType)
      throws HttpException {

    var response =
        StatisticsTopRatesEndpoint.getStatisticsTopRates(CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, contentType);

    response.then();

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify statistics top rates are fetched in JSON format if content type is NOT specified")
  public void getStatisticsTopRatesWithoutSpecifyingContentType() throws HttpException {

    var response =
        StatisticsTopRatesEndpoint.getStatisticsTop(CLIENT_AUTOMATION_ID,
            null, FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,true,null,
        AuthConstants.ENDPOINTS_API_KEY,null);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }
}
