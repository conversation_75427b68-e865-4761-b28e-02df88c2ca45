package com.fansunited.automation.loyaltyapi.statistics;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.bq.events.GameType.Documentation.SINGLE;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.loyaltyapi.StatisticsTopRatesEndpoint;
import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.PredictionType;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Loyalty Api - GET /v1/statistics endpoint happy path tests")
public class GetStatisticsTopRatesTests extends BaseTest {

  @Test
  @DisplayName("Verify getting own statistics and validate user is placed in the respective tier based on earned points")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getStatisticsTop() throws Exception {

    var profileIdFirstPlace = "f3606363-2f80-44a6-a563-2774f59cf9d9";
    var profileIdSecondPlace = "wukQEYDtr4UGGOQYQJbkNuZ5Nks1";

    InsertBigQData.insertRankEvent(10, PredictionMarket.BOTH_TEAMS_SCORE, profileIdFirstPlace, 20,
        null, String.valueOf(PredictionType.SINGLE), true);
    Thread.sleep(1000);
    InsertBigQData.insertRankEvent(10, PredictionMarket.BOTH_TEAMS_SCORE, profileIdSecondPlace, 10,
        null, String.valueOf(PredictionType.SINGLE), true);
    InsertBigQData.insertRankEvent(10, PredictionMarket.CORRECT_SCORE, profileIdFirstPlace, 20,
        null, SINGLE, true);
    Thread.sleep(100);
    InsertBigQData.insertRankEvent(10, PredictionMarket.CORRECT_SCORE, profileIdSecondPlace, 10,
        null, SINGLE, true);

    BigQueryHelper.waitForEventsToBeSaved(60);

    var response =
        StatisticsTopRatesEndpoint.getStatisticsTopRates(CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    response
        .then()
        .log().body()
        .assertThat()
        .statusCode(200)
        .extract()
        .response()
        .jsonPath()
        .getList("data.profile_id")
        .stream()
        .findFirst()
        .equals(profileIdFirstPlace);
  }
}
