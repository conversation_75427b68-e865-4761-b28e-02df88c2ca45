package com.fansunited.automation.loyaltyapi.contest;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.GetContestEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GameEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.base.loyaltyapi.TemplateBaseTest;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import io.restassured.http.ContentType;
import java.time.LocalDateTime;
import java.util.UUID;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

@DisplayName("Loyalty Api - POST /v1/leaderboard/templates endpoint validation tests")
public class ContestTests extends TemplateBaseTest {

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify clients can get contest by contest type")
  public void getContest() throws HttpException {

    var gameId = GamesEndpoint.createGames(GameType.TOP_X, 1).get(0);
    var gameInstance = GameEndpoint.getGameById(gameId).as(GameInstance.class);

    var profileIdFirstPlace = UUID.randomUUID().toString();
    InsertBigQData.insertSingleRankEvent(LocalDateTime.now(), profileIdFirstPlace,
        PredictionMarket.CORRECT_SCORE,
        GameType.TOP_X, 30,
        gameInstance.getId(),
        MatchByIdEndpoint.getMatchDtoById(gameInstance.getFixtures().get(0).getMatchId()), 1, "1",
        LocalDateTime.now().toString());

    var response =
        GetContestEndpoint.getContestByContestType(gameId, CLIENT_AUTOMATION_ID,
            "game",
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            profileIdFirstPlace);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
  }
}
