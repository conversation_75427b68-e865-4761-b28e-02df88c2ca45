package com.fansunited.automation.loyaltyapi.templates.put;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_CHELSEA;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_LIVERPOOL;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.TEAM_ID_MAN_UTD;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.base.AuthBase.createUsers;

import com.fansunited.automation.core.apis.footballapi.MatchesEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplateByIdEndpoint;
import com.fansunited.automation.core.apis.loyaltyapi.TemplatesEndpoint;
import com.fansunited.automation.core.apis.predictionapi.GamesEndpoint;
import com.fansunited.automation.core.base.loyaltyapi.TemplateBaseTest;
import com.fansunited.automation.helpers.DateTripletHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.footballapi.matches.FootballMatchesData;
import com.fansunited.automation.model.footballapi.matches.Match;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateGroupFilter;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateGroups;
import com.fansunited.automation.model.loyaltyapi.templates.request.TemplateRequest;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingColorsDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingImagesDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingUrlsDTO;
import com.fansunited.automation.model.predictionapi.games.Fields;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.validators.LoyaltyApiValidator;
import com.github.javafaker.Faker;
import com.google.firebase.auth.FirebaseAuthException;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Loyalty Api - PUT /v1/leaderboard/templates/{templateId} endpoint happy path tests")
public class UpdateTemplateTests extends TemplateBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify template is successfully updated. Fields: competition_ids, markets, from_date, to_date, rules, flags")
  public void updateTemplate() throws HttpException, IOException, ExecutionException,
      FirebaseAuthException, InterruptedException {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDate = dateTriplet.getFromString();
    var toDate = dateTriplet.getToString();
    var participants = createUsers(3);
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().funnyName().name())
        .fromDate(fromDate)
        .toDate(toDate)
        .rules(new Faker().regexify("[A-Za-z0-9$&+,:;=?@#|'<>.^*()%!-]{10,20}"))
        .matchIds(List.of("fb:m:158469", "fb:m:158476","fb:m:158406",
            "fb:m:158404",
            "fb:m:158409",
            "fb:m:157540",
            "fb:m:163932",
            "fb:m:163919",
            "fb:m:172828",
            "fb:m:172825",
            "fb:m:175479",
            "fb:m:175484",
            "fb:m:170474",
            "fb:m:157539",
            "fb:m:158443",
            "fb:m:158427",
            "fb:m:163926","fb:m:157555","fb:m:175476","fb:m:158413","fb:m:157545","fb:m:157542","fb:m:172835","fb:m:158453","fb:m:170593","fb:m:163929","fb:m:158428","fb:m:172837","fb:m:172854","fb:m:172856","fb:m:172867","fb:m:158416","fb:m:158417","fb:m:158421","fb:m:158435","fb:m:170570","fb:m:157547","fb:m:158415","fb:m:172843","fb:m:175495","fb:m:175517","fb:m:163934","fb:m:157571","fb:m:175493","fb:m:175504","fb:m:170567",
            "fb:m:163913", "fb:m:170585", "fb:m:175497", "fb:m:157579", "fb:m:157554", "fb:m:158450", "fb:m:158454", "fb:m:172853", "fb:m:164022", "fb:m:175501", "fb:m:170586", "fb:m:175488", "fb:m:157552", "fb:m:158438", "fb:m:164020", "fb:m:164005", "fb:m:163996", "fb:m:170613", "fb:m:172863", "fb:m:172838",
            "fb:m:158439", "fb:m:158426", "fb:m:158447", "fb:m:158490", "fb:m:158423", "fb:m:157576", "fb:m:157588", "fb:m:157565", "fb:m:170648", "fb:m:164016",
            "fb:m:172847", "fb:m:170597", "fb:m:172833", "fb:m:164014", "fb:m:170600", "fb:m:158449", "fb:m:158442", "fb:m:158486", "fb:m:157613", "fb:m:157575",
            "fb:m:163949", "fb:m:164000", "fb:m:163976", "fb:m:172874", "fb:m:172839",

            "fb:m:158445", "fb:m:158448", "fb:m:158459", "fb:m:158487", "fb:m:158474", "fb:m:158484", "fb:m:158452", "fb:m:158492", "fb:m:163981", "fb:m:163978",
            "fb:m:164010", "fb:m:163999", "fb:m:163980", "fb:m:175559", "fb:m:175575", "fb:m:175526", "fb:m:175552", "fb:m:175570", "fb:m:175548", "fb:m:172886",
            "fb:m:172896", "fb:m:172882", "fb:m:172888", "fb:m:172879", "fb:m:172891", "fb:m:172895", "fb:m:170625", "fb:m:170629", "fb:m:170661", "fb:m:170640",
            "fb:m:157572", "fb:m:157652", "fb:m:157601", "fb:m:157615", "fb:m:157608", "fb:m:157614", "fb:m:158489", "fb:m:158501", "fb:m:158477", "fb:m:158478")
)
        .labels(Fields.builder()
            .label1("test")
            .label2("test1_?123!?/")
            .build())
        .customFields(Fields.builder()
            .label2("123test")
            .label2("tesgf!/_")
            .build())
        .groups(List.of(TemplateGroups.builder()
            .groupId("test")
            .flags(List.of("test1","test2"))
            .label("test")
            .filters(TemplateGroupFilter
                .builder()
                .fromDate(LocalDate.now().toString())
                .toDate(LocalDate.now().plusDays(1).toString())
                .matchIds(List.of("fb:m:1234"))
                .build())
            .build()))
        .ad_content("<p>тест</p>")
        .flags(new Faker().lorem().words(Helper.generateRandomNumber(1, 3)))
        .description(new Faker().lorem().characters(10, 1000))
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(createTemplateResponse);

    LoyaltyApiValidator.validateTemplateResponse(createTemplateResponse, templateRequest);

    var templateResponse = createTemplateResponse.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    templateRequest.setCompetitionIds(List.of(PREMIER_LEAGUE_COMP_ID));
    templateRequest.setMarkets(
        List.of(PredictionMarket.FT_1X2.getValue(), PredictionMarket.BOTH_TEAMS_SCORE.getValue(),
            PredictionMarket.OVER_GOALS_2_5.getValue()));
    templateRequest.setFromDate(LocalDate.now().minusMonths(6).toString());
    templateRequest.setToDate(LocalDate.now().toString());
    templateRequest.setExcludedProfileIds(List.of(participants.get(0).getUid(),participants.get(1).getUid()));
    templateRequest.setBranding(BrandingDTO.builder()
        .colors(BrandingColorsDTO.builder()
            .additionalColor("test")
            .backgroundColor("")
            .primaryColor("")
            .contentColor("")
            .borderColor("")
            .secondaryColor("")
            .build())
        .urls(BrandingUrlsDTO.builder()
            .primaryUrl("тест")
            .privacyPolicyUrl("тест")
            .additionalUrl("тест")
            .build())
        .images(BrandingImagesDTO.builder()
            .additionalImage("тест")
            .backgroundImage("тест")
            .mainLogo("тест")
            .mobileBackgroundImage("тест")
            .build())
        .build());
    templateRequest.setRules(new Faker().regexify("[A-Za-z0-9$&+,:;=?@#|'<>.^*()%!-]{30,40}"));
    templateRequest.setFlags(new Faker().lorem().words(Helper.generateRandomNumber(1,3)));
    templateRequest.setDescription(new Faker().lorem().words(Helper.generateRandomNumber(10,30)).toString());

    var updateTemplateResponse =
        TemplateByIdEndpoint.updateLeaderboardTemplate(templateResponse.getId(),
            templateRequest);

    LoyaltyApiValidator.validateTemplateResponse(updateTemplateResponse, templateRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify template is successfully updated. Fields: match_ids")
  public void updateTemplateMatchIds() throws HttpException {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDate = dateTriplet.getFromString();
    var toDate = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().funnyName().name())
        .fromDate(fromDate)
        .toDate(toDate)
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(createTemplateResponse);

    LoyaltyApiValidator.validateTemplateResponse(createTemplateResponse, templateRequest);

    var templateResponse = createTemplateResponse.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    var matchIds =
        MatchesEndpoint.getMatches().as(FootballMatchesData.class).getData().stream().map(
            Match::getId).toList();

    templateRequest.setMatchIds(matchIds);

    var updateTemplateResponse =
        TemplateByIdEndpoint.updateLeaderboardTemplate(templateResponse.getId(),
            templateRequest);

    LoyaltyApiValidator.validateTemplateResponse(updateTemplateResponse, templateRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify template is successfully updated. Fields: game_types")
  public void updateTemplateGameTypes() throws HttpException {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDate = dateTriplet.getFromString();
    var toDate = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().funnyName().name())
        .excludedProfileIds(List.of("test"))
        .fromDate(fromDate)
        .toDate(toDate)
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(createTemplateResponse);

    LoyaltyApiValidator.validateTemplateResponse(createTemplateResponse, templateRequest);

    var templateResponse = createTemplateResponse.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    templateRequest.setGameTypes(List.of(GameType.TOP_X.getValue()));

    var updateTemplateResponse =
        TemplateByIdEndpoint.updateLeaderboardTemplate(templateResponse.getId(),
            templateRequest);

    LoyaltyApiValidator.validateTemplateResponse(updateTemplateResponse, templateRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify template is successfully updated. Fields: game_ids")
  public void updateTemplateGameIds() throws HttpException {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDate = dateTriplet.getFromString();
    var toDate = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().funnyName().name())
        .fromDate(fromDate)
        .toDate(toDate)
        .build();

    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(createTemplateResponse);

    LoyaltyApiValidator.validateTemplateResponse(createTemplateResponse, templateRequest);

    var templateResponse = createTemplateResponse.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    var gameIds = GamesEndpoint.createGames(GameType.TOP_X, 2);

    templateRequest.setGameIds(gameIds);

    var updateTemplateResponse =
        TemplateByIdEndpoint.updateLeaderboardTemplate(templateResponse.getId(),
            templateRequest);

    LoyaltyApiValidator.validateTemplateResponse(updateTemplateResponse, templateRequest);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify template is successfully updated. Fields: team_ids, from_date, to_date")
  public void updateTemplateTeamIds() throws HttpException {

    var dateTriplet = DateTripletHelper.getInstance().getNext();
    var fromDate = dateTriplet.getFromString();
    var toDate = dateTriplet.getToString();
    var templateRequest = TemplateRequest.builder()
        .name(new Faker().funnyName().name())
        .fromDate(fromDate)
        .toDate(toDate)
        .groups(
            List.of(
                TemplateGroups.builder()
                    .groupId(new Faker().funnyName().toString())
                    .label("test")
                    .filters(
                        TemplateGroupFilter.builder()
                            .matchIds(null)
                            .fromDate(fromDate)
                            .toDate(toDate)
                            .build())
                    .build()))
        .build();

    var templateUpdateRequest = TemplateRequest.builder()
        .name(new Faker().funnyName().name())
        .fromDate(fromDate)
        .toDate(toDate)
        .groups(
            List.of(
                TemplateGroups.builder()
                    .groupId(new Faker().funnyName().toString())
                    .label("test1")
                    .filters(
                        TemplateGroupFilter.builder()
                            .matchIds(null)
                            .fromDate(fromDate)
                            .toDate(toDate)
                            .build())
                    .build()))
        .build();



    var createTemplateResponse = TemplatesEndpoint.createLeaderboardTemplate(templateRequest);

    currentTestResponse.set(createTemplateResponse);

    LoyaltyApiValidator.validateTemplateResponse(createTemplateResponse, templateRequest);

    var templateResponse = createTemplateResponse.as(TemplateResponse.class);

    cleanUpTemplateIdList.add(templateResponse.getId());

    templateRequest.setTeamIds(List.of(TEAM_ID_LIVERPOOL, TEAM_ID_MAN_UTD, TEAM_ID_CHELSEA));
    templateRequest.setFromDate(LocalDate.now().minusYears(1).toString());
    templateRequest.setToDate(LocalDate.now().toString());

    var updateTemplateResponse =
        TemplateByIdEndpoint.updateLeaderboardTemplate(templateResponse.getId(),
            templateUpdateRequest);

    updateTemplateResponse
        .then()
            .assertThat();

    LoyaltyApiValidator.validateTemplateResponse(updateTemplateResponse, templateUpdateRequest);
  }
}
