package com.fansunited.automation.clientsapi.staff.put;

import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.junit.internal.matchers.StringContains.containsString;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.StaffMemberEndpoint;
import com.fansunited.automation.core.base.BaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.clientapi.CreateStaffMember;
import com.fansunited.automation.model.clientapi.SetGlobalRolesForStaffMember;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Client Api - PATCH /v1/clients/{client_id}/staff/{user_id} endpoint happy path test")
@Execution(ExecutionMode.SAME_THREAD)
public class SetGlobalRolesTests extends BaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify that staff member can be updated with global roles")
  public void createStaffWithGlobalRoles() throws IllegalArgumentException, HttpException {

    var createStaff =
        StaffMemberEndpoint.createStaff(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS,
            CreateStaffMember.builder()
                .email(new Faker().internet().emailAddress())
                .name(new Faker().funnyName().name().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
                .pass(new Faker().internet().password())
                .roles(List.of("client_editor"))
                .build(), CLIENT_AUTOMATION_ID);

    createStaff
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    var userId = createStaff.then().extract().body().jsonPath().getString("data.id");

    var setGlobalRolesStaff =
        StaffMemberEndpoint.setGlobalRolesForStaff(
            FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, SetGlobalRolesForStaffMember.builder()
                .roles(List.of("platform_operator"))
                .build(), CLIENT_AUTOMATION_ID, userId, AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    setGlobalRolesStaff
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body("data.roles[0].", containsString("platform_operator"));
  }
}
