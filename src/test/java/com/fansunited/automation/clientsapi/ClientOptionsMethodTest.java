package com.fansunited.automation.clientsapi;

import static com.fansunited.automation.constants.Endpoints.ClientApi.CLIENTS;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.core.apis.clientapi.ClientEndpoint.optionsClient;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import io.restassured.response.Response;
import java.util.Arrays;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName("Clint API - OPTIONS method Checks the allowed methods for Client API.")
@Execution(ExecutionMode.SAME_THREAD)
public class ClientOptionsMethodTest extends ClientApiBaseTest {

  @Test
  @DisplayName(
      "Verify the client API endpoints using the OPTIONS method. Endpoint: OPTIONS /v1/clients")
  @Tag(SMOKE)
  public void optionsMethodTest() throws HttpException {
    Response response = optionsClient(CLIENTS);
    response.then().assertThat().statusCode(HttpStatus.SC_OK);

    var actualMethods =
        Arrays.stream(response.getHeaders().getValues("Allow").get(0).split(", ")).toList();
    var expectedMethods = ApiConstants.HttpMethods.getValuesAsList();

    assertThat(actualMethods).as(EMPTY_LIST_MESSAGE).isNotEmpty().isNotNull();
    Assertions.assertTrue(expectedMethods.containsAll(actualMethods), METHODS_MISMATCH_MESSAGE);
  }
}
