package com.fansunited.automation.clientsapi.lists.post;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.validators.ListValidator.validateCreateListResponse;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ManageListsEndpoint;
import com.fansunited.automation.core.apis.clientapi.enums.managelistsenums.ListTypes;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("Client Api - POST /v1/clients/{client_id}/lists endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class CreateClientListTests extends ClientApiBaseTest {

  @ParameterizedTest(
      name = "Ensure the create lists feature is working correctly for list type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Ensure the create lists feature is working correctly")
  @EnumSource(value = ListTypes.class)
  public void createListTest(ListTypes listType) throws Exception {

    var request = createRequestByListType(listType);

    var response =
        ManageListsEndpoint.createList(
            true,
            FANS_UNITED_CLIENTS,
            ADMIN_USER,
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    currentTestResponse.set(response);

    validateCreateListResponse(request, response);
  }
}
