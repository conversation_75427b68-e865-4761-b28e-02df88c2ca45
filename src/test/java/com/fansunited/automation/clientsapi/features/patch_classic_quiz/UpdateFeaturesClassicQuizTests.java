package com.fansunited.automation.clientsapi.features.patch_classic_quiz;

import static com.fansunited.automation.constants.AuthConstants.DEFAULT_USER_PASS;
import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_CLASSIC_QUIZ;
import static com.fansunited.automation.constants.RegexConstants.SPECIAL_CHARACTERS_TO_BE_REPLACED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static java.util.List.of;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.apis.clientapi.StaffMemberEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.CreateStaffMember;
import com.fansunited.automation.model.clientapi.features.response.BasicObject;
import com.fansunited.automation.model.clientapi.features.response.ClassicQuizFeature;
import com.fansunited.automation.model.clientapi.features.response.FeaturesResponse;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName(
    "Client Api - GET /v1/clients/{clientId}/feature/classic_quiz endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateFeaturesClassicQuizTests extends ClientApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify client can update quiz features")
  public void clientCanUpdateQuizFeatures() throws HttpException {

    var label = new Faker().name().title();
    var id = new Faker().idNumber().toString();
    // Create staff member that will be used to update client actions in features
    var staff =
        CreateStaffMember.builder()
            .name(new Faker().name().firstName().replaceAll(SPECIAL_CHARACTERS_TO_BE_REPLACED, " "))
            .email(new Faker().internet().emailAddress())
            .pass(DEFAULT_USER_PASS)
            .roles(of("client_admin"))
            .build();

    var createStaffMember =
        StaffMemberEndpoint.createStaff(FANS_UNITED_CLIENTS, staff, CLIENT_AUTOMATION_ID);

    currentTestResponse.set(createStaffMember);

    createStaffMember.then().assertThat().statusCode(HttpStatus.SC_OK);

    ClassicQuizFeature quizFeatureBody =
        ClassicQuizFeature.builder()
            .types(List.of(BasicObject.builder().label(label).id(id).build()))
            .build();

    var updateQuizFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            staff.getEmail(),
            quizFeatureBody,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_CLASSIC_QUIZ);

    updateQuizFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    var response =
        ClientFeaturesEndpoint.getClientsByIdFeatures(
                CLIENT_AUTOMATION_ID,
                AuthConstants.ENDPOINTS_API_KEY,
                ContentType.JSON,
                FANS_UNITED_CLIENTS)
            .as(FeaturesResponse.class);

    response.getData().getClassic_quiz().getTypes().equals(label);
  }
}
