package com.fansunited.automation.clientsapi.features.football_fantasy;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.BUNDESLIGA_COMP_ID;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.PREMIER_LEAGUE_COMP_ID;
import static com.fansunited.automation.constants.Endpoints.ClientApi.FEATURES_FANTASY_GAME;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.validators.FantasyValidator.validateFantasyFeaturesResponse;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientFeaturesEndpoint;
import com.fansunited.automation.core.base.clientapi.ClientApiBaseTest;
import com.fansunited.automation.model.clientapi.features.response.AssistsExtended;
import com.fansunited.automation.model.clientapi.features.response.Coefficients;
import com.fansunited.automation.model.clientapi.features.response.ConcededGoalsExtended;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.Multipliers;
import com.fansunited.automation.model.clientapi.features.response.FootballFantasyFeature;
import com.fansunited.automation.model.clientapi.features.response.FoulsCommittedExtended;
import com.fansunited.automation.model.clientapi.features.response.Players;
import com.fansunited.automation.model.clientapi.features.response.SavesExtended;
import com.fansunited.automation.model.clientapi.features.response.ShotsOnExtended;
import com.fansunited.automation.model.clientapi.features.response.TacklesExtended;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName(
    "Client Api - PATCH /v1/clients/{clientId}/feature/football_fantasy endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class UpdateFootballFantasyTests extends ClientApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify client can update football fantasy features")
  public void clientCanUpdateFantasyFeatures() throws HttpException {

    FootballFantasyFeature fantasyFeature =
        FootballFantasyFeature.builder()
            .enabled(true)
            .multipliers(Multipliers.builder().captain(1).viceCaptain(1).build())
            .coefficients(
                Coefficients.builder()
                    .assists(4)
                    .shots(1)
                    .offsides(-1)
                    .tackles(1)
                    .saves(3)
                    .minutesEqualOrOver60(3)
                    .minutesUnder60(2)
                    .yellowCards(-1)
                    .redCards(-3)
                    .goalsGoalkeeper(5)
                    .goalsDefender(5)
                    .goalsMidfielder(5)
                    .goalsForwards(5)
                    .penaltyGoals(3)
                    .ownGoals(-3)
                    .shotsOn(2)
                    .assistsExtended(new AssistsExtended(0, 0, 3, 5))
                    .savesExtended(new SavesExtended(0, 0, 1, 1, 1, 2))
                    .tacklesExtended(new TacklesExtended(1, 1, 2, 2, 3))
                    .shotsOnExtended(new ShotsOnExtended(0, 0, 1, 1, 1, 2, 2, 2, 2, 3))
                    .concededGoalsExtended(new ConcededGoalsExtended(-1, -2, -3, -4, -5))
                    .foulsCommittedExtended(new FoulsCommittedExtended(-1, -2, -3, -4, -5))
                    .cleanSheetsGoalkeeper(1)
                    .cleanSheetsDefender(1)
                    .cleanSheetsMidfielder(1)
                    .cleanSheetsForwards(1)
                    .foulsCommitted(-1)
                    .penaltyCommitted(-2)
                    .penaltyWon(3)
                    .penaltyMissed(-3)
                    .concededGoals(-1)
                    .caughtBall(3)
                    .build())
            .add_points_to_profileTotal(true)
            .multipliers(Multipliers.builder()
                .viceCaptain(1)
                .captain(1)
                .build())
            .competitions_whitelist(List.of(PREMIER_LEAGUE_COMP_ID, BUNDESLIGA_COMP_ID))
            .players(
                Players.builder()
                    .total(11)
                    .minGoalkeepers(1)
                    .maxGoalkeepers(1)
                    .minDefenders(1)
                    .maxDefenders(1)
                    .minMidfielders(1)
                    .maxMidfielders(1)
                    .minStrikers(1)
                    .maxStrikers(1)
                    .build())
            .build();

    var updateFantasyFeaturesResponse =
        ClientFeaturesEndpoint.updateFeatures(
            FANS_UNITED_CLIENTS,
            null,
            fantasyFeature,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FEATURES_FANTASY_GAME);

    updateFantasyFeaturesResponse.then().assertThat().statusCode(HttpStatus.SC_OK);

    var response =
        ClientFeaturesEndpoint.getClientsByIdFeatures(
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FANS_UNITED_CLIENTS);

    validateFantasyFeaturesResponse(response, fantasyFeature);
  }
}
