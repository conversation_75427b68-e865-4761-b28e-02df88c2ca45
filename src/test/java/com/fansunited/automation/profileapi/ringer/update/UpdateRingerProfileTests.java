package com.fansunited.automation.profileapi.ringer.update;

import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.AVATAR_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.BIRTHDATE_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CHANGE_RINGER_PROFILE_EMAIL_TYPE;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CREATED_AT;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.CREATE_RINGER_PROFILE_TYPE;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.GENDER_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NAME_PROP;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.NICKNAME;
import static com.fansunited.automation.constants.ApiConstants.ProfileApi.UPDATE_RINGER_PROFILE_TYPE;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.PROFILE_AVATAR_URL;
import static org.apache.http.HttpStatus.SC_OK;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.equalToCompressingWhiteSpace;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.JsonSchemasPath;
import com.fansunited.automation.core.apis.profileapi.RingerProfileEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import io.restassured.module.jsv.JsonSchemaValidator;
import io.restassured.response.Response;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Profile Api Ringer - Update POST /v1/webhooks/ringer endpoint happy path tests")
public class UpdateRingerProfileTests extends ProfileApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag("FZ-1744")})
  @DisplayName("Update event for ringier profiles create profiles in Firebase")
  public void verifyUpdateEventCreateProfile() throws HttpException {

    Faker faker = new Faker();
    var profileId = faker.regexify("[a-z0-9]{32}");
    var email = faker.internet().emailAddress();
    // Update the Ringer Profile
    String avatar = PROFILE_AVATAR_URL;
    String birthdate = "1900-01-01";
    String updatedNickname = new Faker().name().username();
    String updateLastName = new Faker().name().lastName();
    String updateFirstName = new Faker().name().firstName();

    ProfileData.Profile updatedProfile = new ProfileData.Profile();
    updatedProfile.setGender(ProfileData.Profile.Gender.FEMALE);
    updatedProfile.setAvatar(avatar);
    updatedProfile.setEmail(email);
    updatedProfile.setFirstName(updateFirstName);
    updatedProfile.setName(updateFirstName+ ""+ updateLastName);
    updatedProfile.setLastName(updateLastName);
    updatedProfile.setBirthDate(birthdate);
    updatedProfile.setNickname(updatedNickname);

    RingerProfileEndpoint ringerProfileEndpoint = RingerProfileEndpoint.builder()
        .type(UPDATE_RINGER_PROFILE_TYPE)
        .sub(profileId)
        .clientId(CLIENT_AUTOMATION_ID)
        .apiKey(AuthConstants.ENDPOINTS_API_KEY)
        .contentType(ContentType.JSON)
        .build();

    Response upadeteResponse = ringerProfileEndpoint.updateRingerProfile(true, updatedProfile);

    currentTestResponse.set(upadeteResponse);

    upadeteResponse
        .then()
        .statusCode(SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.GET_RINGER_PROFILES_SCHEMA))
        .body("data." + ID_PROP, equalTo(profileId))
        .body("data." + AVATAR_PROP, equalTo(avatar))
        .body("data." + NAME_PROP,
            equalToCompressingWhiteSpace(updateFirstName + " " + updateLastName))
        .body("data." + GENDER_PROP, equalTo(ProfileData.Profile.Gender.FEMALE.getValue()))
        .body("data." + BIRTHDATE_PROP, equalTo(birthdate))
        .body("data." + NICKNAME, equalTo(updatedNickname))
        .body("data." + CREATED_AT, is(notNullValue()));

    RingerProfileEndpoint ringerCreateProfileEndpoint = RingerProfileEndpoint.builder()
        .type(CREATE_RINGER_PROFILE_TYPE)
        .sub(profileId)
        .email(email)
        .clientId(CLIENT_AUTOMATION_ID)
        .apiKey(AuthConstants.ENDPOINTS_API_KEY)
        .contentType(ContentType.JSON)
        .build();

    Response createResponse = ringerCreateProfileEndpoint.createRingerProfile(true);

    createResponse
        .then()
        .statusCode(SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.GET_RINGER_PROFILES_SCHEMA))
        .body("data." + ID_PROP, equalTo(profileId));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Update Ringer Profile")
  public void updateRingerProfile() throws HttpException {

    Faker faker = new Faker();

    var profileId = faker.regexify("[a-z0-9]{32}");
    var name= faker.funnyName().name();
    var email = faker.internet().emailAddress();

    // Create new Ringer Profile
    RingerProfileEndpoint ringerProfileEndpoint = RingerProfileEndpoint.builder()
        .type(CREATE_RINGER_PROFILE_TYPE)
        .sub(profileId)
        .name(name)
        .email(email)
        .clientId(CLIENT_AUTOMATION_ID)
        .apiKey(AuthConstants.ENDPOINTS_API_KEY)
        .contentType(ContentType.JSON)
        .build();

    Response response = ringerProfileEndpoint.createRingerProfile(true);

    response
        .then()
        .statusCode(SC_OK);

    // Update the Ringer Profile
    String avatar = PROFILE_AVATAR_URL;
    String birthdate = "1900-01-02";
    String updatedNickname = new Faker().name().username();
    String updateLastName = new Faker().name().lastName();
    String updateFirstName = new Faker().name().firstName();

    ProfileData.Profile updatedProfile = new ProfileData.Profile();
    updatedProfile.setGender(ProfileData.Profile.Gender.FEMALE);
    updatedProfile.setAvatar(avatar);
    updatedProfile.setName(name);
    updatedProfile.setFirstName(updateFirstName);
    updatedProfile.setLastName(updateLastName);

    updatedProfile.setBirthDate(birthdate);
    updatedProfile.setNickname(updatedNickname);

    ringerProfileEndpoint = RingerProfileEndpoint.builder()
        .type(UPDATE_RINGER_PROFILE_TYPE)
        .sub(profileId)
        .clientId(CLIENT_AUTOMATION_ID)
        .apiKey(AuthConstants.ENDPOINTS_API_KEY)
        .contentType(ContentType.JSON)
        .build();

    response = ringerProfileEndpoint.updateRingerProfile(true, updatedProfile);

    currentTestResponse.set(response);

    response
        .then()
        .statusCode(SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.GET_RINGER_PROFILES_SCHEMA))
        .body("data." + ID_PROP, equalTo(profileId))
        .body("data." + AVATAR_PROP, equalTo(avatar))
        .body("data." + GENDER_PROP, equalTo(ProfileData.Profile.Gender.FEMALE.getValue()))
        .body("data." + BIRTHDATE_PROP, equalTo(birthdate))
        .body("data." + NICKNAME, equalTo(updatedNickname))
        .body("data." + CREATED_AT, is(notNullValue()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Update Ringer Profile email")
  public void updateRingerProfileEmail() throws HttpException {

    Faker faker = new Faker();

    var profileId = faker.regexify("[a-z0-9]{32}");
    var email = faker.internet().emailAddress();

    // Create new Ringer Profile
    RingerProfileEndpoint ringerProfileEndpoint = RingerProfileEndpoint.builder()
        .type(CREATE_RINGER_PROFILE_TYPE)
        .sub(profileId)
        .email(email)
        .clientId(CLIENT_AUTOMATION_ID)
        .apiKey(AuthConstants.ENDPOINTS_API_KEY)
        .contentType(ContentType.JSON)
        .build();

    Response response = ringerProfileEndpoint.createRingerProfile(true);

    response
        .then()
        .statusCode(SC_OK);

    // Change the Ringer Profile Email
    String updatedEmail = faker.internet().emailAddress();

    ringerProfileEndpoint = RingerProfileEndpoint.builder()
        .type(CHANGE_RINGER_PROFILE_EMAIL_TYPE)
        .sub(profileId)
        .old_email(email)
        .new_email(updatedEmail)
        .clientId(CLIENT_AUTOMATION_ID)
        .apiKey(AuthConstants.ENDPOINTS_API_KEY)
        .contentType(ContentType.JSON)
        .build();

    response = ringerProfileEndpoint.changeRingerProfileEmail(true);

    currentTestResponse.set(response);

    response
        .then()
        .statusCode(SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(
            JsonSchemasPath.ProfileApi.Endpoints.Profile.GET_RINGER_PROFILES_SCHEMA))
        .body("data." + ID_PROP, equalTo(profileId));
  }
}
