package com.fansunited.automation.profileapi.badges.users;

import static com.fansunited.automation.constants.JsonSchemasPath.ReportingApi.Endpoints.Users.GET_TOTAL_USERS_PER_PERIOD_SCHEMA;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.helpers.DateFormatter.ISO8601_WITH_NANO;
import static com.fansunited.automation.helpers.Helper.convertLocalDateToDateTime;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.core.apis.mockapi.Periodicity;
import com.fansunited.automation.core.apis.reportingapi.TotalUsersEndpoint;
import com.fansunited.automation.core.apis.reportingapi.enums.GroupBy;
import com.fansunited.automation.core.base.reportingapi.ReportingApiBaseTest;
import com.fansunited.automation.helpers.BigQueryHelper;
import com.fansunited.automation.helpers.bq.InsertBigQData;
import com.fansunited.automation.model.footballapi.common.Country;
import com.fansunited.automation.model.reportingapi.mock.CountryProfile;
import com.fansunited.automation.model.reportingapi.mock.RegistrationProfile;
import com.fansunited.automation.model.reportingapi.users.totalusers.TotalUsersDto;
import com.fansunited.automation.validators.CacheValidator;
import com.github.javafaker.Faker;
import io.restassured.module.jsv.JsonSchemaValidator;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Reporting Api - GET /v1/users/total endpoint happy path tests")
public class GetTotalUsersTests extends ReportingApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE), @Tag(DISABLED), @Tag("FZ-772"), @Tag("FZ-778")})
  @DisplayName("Verify getting total users per period")
  public void getTotalUsersPerPeriod() throws HttpException, InterruptedException {

    var fromDate = LocalDate.now().minusYears(3).minusDays(6);
    var toDate = LocalDate.now().minusYears(3);

    var events =
        InsertBigQData.generateRegistrationEventsForPeriod(generateRandomNumber(1, 5), fromDate,
            toDate, Periodicity.DAY);

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response = TotalUsersEndpoint.getTotalUsersForPeriod(fromDate.toString(),
        toDate.toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TOTAL_USERS_PER_PERIOD_SCHEMA));

    currentTestResponse.set(response);

    var totalUsersDto = response.as(TotalUsersDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        totalUsersDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        totalUsersDto.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        totalUsersDto.getMeta().getGroupedBy(), equalTo(GroupBy.NONE));

    MatcherAssert.assertThat("Total users per period returned by the API is incorrect",
        totalUsersDto.getData().getAll(),
        is((long) events.values().stream().mapToInt(Long::intValue).sum()));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify events with same profile id are not counted multiple times when getting total users per period")
  public void getTotalUsersPerPeriodDuplicateProfileId()
      throws HttpException, InterruptedException {

    var fromDate = LocalDate.now().minusDays(22);
    var toDate = LocalDate.now().minusDays(21);

    var profileId = UUID.randomUUID().toString();
    var profileCreatedAt = convertLocalDateToDateTime(fromDate);
    var profileUpdatedAt = profileCreatedAt;

    var profile = RegistrationProfile.builder()
        .id(profileId)
        .name(new Faker().funnyName().name())
        .gender("unspecified")
        .country(CountryProfile.builder()
            .id(ApiConstants.ProfileApi.COUNTRY_ID_BG)
            .name("Bulgaria")
            .assets(new Country.AssetsFlag(
                "https://profile.fansunitedassets.com/country/3a92ffe9-8e19-11eb-b60d-42010a84003b.png"))
            .build())
        .followersCount(0)
        .followingCount(0)
        .email(new Faker().internet().emailAddress())
        .avatar("http://noavatar.com").build();

    InsertBigQData.insertSingleProfile(profileCreatedAt, profile, 100, null);

    var eventsCount = 3;
    for (int i = 0; i < eventsCount; i++) {
      // Simulate profile update
      profileUpdatedAt = profileUpdatedAt.plusMinutes(1);
      profile.setUpdatedAt(profileUpdatedAt.format(
          DateTimeFormatter.ofPattern(ISO8601_WITH_NANO)));
      InsertBigQData.insertSingleProfile(profileCreatedAt, profile,
          100, profileUpdatedAt);
    }

    BigQueryHelper.waitForEventsToBeSaved(10); // Wait for events to be saved to BigQuery

    var response = TotalUsersEndpoint.getTotalUsersForPeriod(fromDate.toString(),
        toDate.toString());

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .body(JsonSchemaValidator.matchesJsonSchemaInClasspath(GET_TOTAL_USERS_PER_PERIOD_SCHEMA));

    currentTestResponse.set(response);

    var totalUsersDto = response.as(TotalUsersDto.class);

    MatcherAssert.assertThat("Meta prop -> from_date is incorrect",
        totalUsersDto.getMeta().getFromDate(), equalTo(fromDate));
    MatcherAssert.assertThat("Meta prop -> to_date is incorrect",
        totalUsersDto.getMeta().getToDate(), equalTo(toDate));
    MatcherAssert.assertThat("Meta prop -> group_by filter is incorrect",
        totalUsersDto.getMeta().getGroupedBy(), equalTo(GroupBy.NONE));

    MatcherAssert.assertThat("Total users per period returned by the API is incorrect",
        totalUsersDto.getData().getAll(),
        equalTo(1L));
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify GET /v1/users/total response returned by the server is NOT cached")
  public void verifyGetTotalRegisteredUsersResponseIsNotCached()
      throws HttpException {

    var response =
        TotalUsersEndpoint.getTotalUsersForPeriod(LocalDate.now().minusDays(1).toString(),
            LocalDate.now().toString());

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);

    CacheValidator.validateResponseIsCached(response);
  }
}
