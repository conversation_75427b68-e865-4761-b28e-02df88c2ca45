package com.fansunited.automation.profileapi.badges;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.REMOTE_ONLY;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.profileapi.BadgesByProfileIdEndpoint;
import com.fansunited.automation.core.base.profileapi.BadgesBaseTest;
import com.fansunited.automation.validators.ErrorValidator;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Profile Api - GET /v1/profile/badges/{userId} endpoint validation tests")
public class GetBadgesForProfileValidationTests extends BadgesBaseTest {

  @ParameterizedTest(name = "Verify badges for user cannot be fetched with invalid/missing api key. Api key: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(REMOTE_ONLY)})
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  public void getBadgesForUserWithInvalidApiKey(
      InvalidEndpointsApiKeyArgumentsHolder argumentsHolder) throws HttpException {

    var response =
        BadgesByProfileIdEndpoint.getBadgesForProfile(getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(), ContentType.JSON);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting badges for user with invalid client id. Client ID: {arguments}")
  @Tag(REGRESSION)
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getBadgesForUserWithInvalidClientId(String clientId)
      throws HttpException {

    var response =
        BadgesByProfileIdEndpoint.getBadgesForProfile(getCurrentTestUser().getUid(), clientId,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify API returns NOT_FOUND when getting badges for user with invalid user id")
  public void getBadgesForUserWithInvalidUserId() throws HttpException {

    var response =
        BadgesByProfileIdEndpoint.getBadgesForProfile("asxxvf3vaJEUr1sdr3q");

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_NOT_FOUND);
  }

  @Disabled
  @ParameterizedTest(name = "Verify API returns BAD_REQUEST when getting badges for user with non supported content type. Content type: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag("FZ-1115")})
  @EnumSource(value = ContentType.class, mode = EnumSource.Mode.EXCLUDE, names = {"JSON", "ANY"})
  public void getBadgesForUserWithNonSupportedContentType(ContentType contentType)
      throws HttpException {

    var response =
        BadgesByProfileIdEndpoint.getBadgesForProfile(getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, contentType);

    currentTestResponse.set(response);

    ErrorValidator.validateErrorResponse(response, HttpStatus.SC_BAD_REQUEST);
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify badges for user are fetched in JSON format if content type is NOT specified")
  public void getBadgesForUserWithoutSpecifyingContentType() throws HttpException {

    var response =
        BadgesByProfileIdEndpoint.getBadgesForProfile(getCurrentTestUser().getUid(),
            CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK)
        .contentType(ContentType.JSON);
  }
}
