package com.fansunited.automation.profileapi.leads;

import static com.fansunited.automation.constants.AuthConstants.ENDPOINTS_API_KEY;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.LeadGenerator.createSingleLead;
import static com.fansunited.automation.helpers.LeadGenerator.generateRandomCreateLeadRequest;
import static com.fansunited.automation.validators.ProfileApiValidator.validateGetLeadByIdResponse;

import com.fansunited.automation.core.apis.profileapi.leads.GetLeadByIdEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.model.profileapi.leades.CreateLeadRequest;
import com.fansunited.automation.model.profileapi.leades.LeadDto;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Profile Api - GET /v1/leads/{lead_id} endpoint happy path tests")
public class GetLeadByIdTests extends ProfileApiBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verifies that lead data is successfully fetched by its id")
  public void getLeadDataSuccessfully() throws HttpException {

    LeadDto lead;
    CreateLeadRequest request = generateRandomCreateLeadRequest();

    try {
      lead = createSingleLead(request).jsonPath().getObject("data", LeadDto.class);

    } catch (Exception e) {
      throw new RuntimeException("Failed to generate lead ID", e);
    }

    var response =
        GetLeadByIdEndpoint.getLeadById(
            lead.getLeadId(),
            ADMIN_USER,
            CLIENT_AUTOMATION_ID,
            ENDPOINTS_API_KEY,
            ContentType.JSON,
            true,
            null);

    validateGetLeadByIdResponse(response, lead);
  }
}
