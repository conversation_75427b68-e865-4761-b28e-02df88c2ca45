package com.fansunited.automation.profileapi.leads;

import static com.fansunited.automation.constants.ApiErrorCodes.INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.LeadGenerator.generateRandomCreateLeadRequest;
import static org.hamcrest.Matchers.equalTo;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.profileapi.leads.CreateLeadEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.model.profileapi.leades.CreateLeadRequest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Profile Api - POST /v1/leads endpoint validation tests")
public class CreateLeadsValidationTests extends ProfileApiBaseTest {
  private static CreateLeadRequest request;

  @BeforeEach
  public void setup() {
    request = generateRandomCreateLeadRequest();
  }

  @ParameterizedTest(
      name = "Verify lead cannot be created with invalid/missing api key. Api key: {arguments}")
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void createLeadWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        CreateLeadEndpoint.createLead(
            null,
            request,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            true,
            null);

    response.then().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name = "Verify lead cannot be created with invalid JWT token. Token: {arguments}")
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void createLeadWithInvalidToken(InvalidJwtTokenArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        CreateLeadEndpoint.createLead(
            null,
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            true,
            argumentsHolder.getJwtToken());

    response.then().statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @DisplayName("Verify lead cannot be created with invalid JWT token. Token: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void createLeadWithoutToken() throws HttpException {

    var response =
        CreateLeadEndpoint.createLead(
            null,
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            false,
            null);

    response.then().statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @ParameterizedTest(
      name = "Verify lead cannot be created with invalid client ID. Client ID: {arguments}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void createLeadWithInvalidClientId(String clientId) throws HttpException {

    var response =
        CreateLeadEndpoint.createLead(
            null, request, clientId, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, true, null);

    response.then().statusCode(HttpStatus.SC_BAD_REQUEST);
  }

  @Test()
  @Tag(REGRESSION)
  @DisplayName(
      "Verify API returns UNSUPPORTED_MEDIA_TYPE when creating lead with non supported content type")
  public void createLeadWithNotSupportedContentType() throws HttpException {
    var response =
        CreateLeadEndpoint.createLead(
            null,
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.XML,
            true,
            null);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(INVALID_CONTENT_TYPE));
  }

  @Test
  @Tag(REGRESSION)
  @DisplayName("Verify lead cannot be created if content type is NOT specified")
  public void createLeadWithoutSpecifyingContentType() throws HttpException {

    var response =
        CreateLeadEndpoint.createLead(
            null, request, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, null, true, null);

    currentTestResponse.set(response);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(INVALID_CONTENT_TYPE));
  }
}
