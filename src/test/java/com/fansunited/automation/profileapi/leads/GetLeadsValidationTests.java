package com.fansunited.automation.profileapi.leads;

import static com.fansunited.automation.constants.ApiErrorCodes.INVALID_CONTENT_TYPE;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.TestGroups.DISABLED;
import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.Matchers.in;

import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsHolder;
import com.fansunited.automation.arguments.commonarguments.InvalidEndpointsApiKeyArgumentsProvider;
import com.fansunited.automation.arguments.commonarguments.InvalidJwtTokenArgumentsHolder;
import com.fansunited.automation.arguments.profileapi.InvalidJwtTokenArgumentsProvider;
import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.core.apis.profileapi.leads.GetLeadEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ArgumentsSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("Profile Api - GET /v1/leads endpoint validation tests")
public class GetLeadsValidationTests extends ProfileApiBaseTest {

  @ParameterizedTest(name = "Verifies that leads fetching fails with invalid/missing API key: {0}")
  @ArgumentsSource(InvalidEndpointsApiKeyArgumentsProvider.class)
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getLeadWithInvalidApiKey(InvalidEndpointsApiKeyArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        GetLeadEndpoint.getLead(
            -1,
            null,
            null,
            null,
            ADMIN_USER,
            CLIENT_AUTOMATION_ID,
            argumentsHolder.getApiKey(),
            ContentType.JSON,
            true,
            null);

    response.then().statusCode(argumentsHolder.getStatusCode());
  }

  @ParameterizedTest(
      name = "Verify leads cannot be fetched with invalid JWT token. Token: {arguments}")
  @ArgumentsSource(InvalidJwtTokenArgumentsProvider.class)
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getLeadsWithInvalidToken(InvalidJwtTokenArgumentsHolder argumentsHolder)
      throws HttpException {

    var response =
        GetLeadEndpoint.getLead(
            -1,
            null,
            null,
            null,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            true,
            argumentsHolder.getJwtToken());

    response.then().statusCode(argumentsHolder.getStatusCode());
  }

  @Test
  @DisplayName("Verifies that lead fetching fails when JWT token is missing: {0}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  public void getLeadWithoutToken() throws HttpException {
    var response =
        GetLeadEndpoint.getLead(
            -1,
            null,
            null,
            null,
            null,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            false,
            null);

    response.then().statusCode(HttpStatus.SC_UNAUTHORIZED);
  }

  @ParameterizedTest(name = "Verifies that lead fetching fails with invalid client ID: {0}")
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @ValueSource(strings = UrlParamValues.ProfileApi.INVALID_CLIENT_ID)
  @NullAndEmptySource
  public void getLeadWithInvalidClientId(String clientId) throws HttpException {

    var response =
        GetLeadEndpoint.getLead(
            -1,
            null,
            null,
            null,
            ADMIN_USER,
            clientId,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            true,
            null);

    response.then().statusCode(in(List.of(HttpStatus.SC_BAD_REQUEST, HttpStatus.SC_FORBIDDEN)));
  }

  @Disabled("WIP - FZ-1640, returns SC_CODE = 200")
  @ParameterizedTest(
      name =
          "Verifies that API returns UNSUPPORTED_MEDIA_TYPE when fetching leads with unsupported content type: Content Type {0}")
  @Tags({@Tag(REGRESSION), @Tag(DISABLED)})
  @EnumSource(
      value = ContentType.class,
      mode = EnumSource.Mode.EXCLUDE,
      names = {"JSON", "ANY"})
  public void getLeadWithNotSupportedContentType(ContentType contentType) throws HttpException {
    var response =
        GetLeadEndpoint.getLead(
            -1,
            null,
            null,
            null,
            ADMIN_USER,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            contentType,
            true,
            null);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_UNSUPPORTED_MEDIA_TYPE)
        .body("error.status", equalTo(INVALID_CONTENT_TYPE));
  }
}
