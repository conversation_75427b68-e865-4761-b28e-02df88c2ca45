package com.fansunited.automation.profileapi.leads;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.LeadGenerator.generateRandomCreateLeadRequest;
import static org.hamcrest.CoreMatchers.notNullValue;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.profileapi.leads.CreateLeadEndpoint;
import com.fansunited.automation.core.base.profileapi.ProfileApiBaseTest;
import com.fansunited.automation.model.profileapi.leades.CreateLeadRequest;
import io.restassured.http.ContentType;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName("Profile Api - POST /v1/leads endpoint happy path tests")
public class CreateLeadsTests extends ProfileApiBaseTest {

  private static CreateLeadRequest request;

  @BeforeEach
  public void setup() {
    request = generateRandomCreateLeadRequest();
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify lead successfully created")
  public void verifyLeadCreation() throws HttpException {

    var response =
        CreateLeadEndpoint.createLead(
            null,
            request,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            true,
            null);

    response.then().statusCode(HttpStatus.SC_OK).body("data", notNullValue());
  }
}
