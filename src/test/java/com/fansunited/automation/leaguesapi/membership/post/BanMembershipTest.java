package com.fansunited.automation.leaguesapi.membership.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.core.apis.leagueapi.leagues.CreateLeagueEndpoint.createLeague;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static org.hamcrest.Matchers.hasItem;

import com.fansunited.automation.core.apis.leagueapi.membership.BanEndpoint;
import com.fansunited.automation.core.apis.leagueapi.membership.JoinEndpoint;
import com.fansunited.automation.core.base.leaguesapi.LeagueBaseTest;
import com.fansunited.automation.model.leaguesapi.request.membership.BaseMembershipRequest;
import com.fansunited.automation.model.leaguesapi.response.LeagueData;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

@DisplayName(
    "League - Membership Api - POST /v1/membership/ban/{leagueId} endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class BanMembershipTest extends LeagueBaseTest {

  @DisplayName("Ensure that ban users functionality works.")
  @Test
  @Tag(REGRESSION)
  public void adminCanBanUsers()
      throws IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException,
          HttpException {

    String email = getCurrentTestUser().getEmail();
    var request = leagueRequest;
    request.setAdministrators(Collections.singletonList(getCurrentTestUser().getUid()));
    var league = createLeague(request, null, email).as(LeagueData.class);
    String leagueId = league.getLeague().getId();
    String invitationCode = league.getLeague().getInvitationCode();

    List<UserRecord> users = createUsers(generateRandomNumber(2, 5));
    List<String> membersIds = new ArrayList<>();
    for (UserRecord userRecord : users) {
      JoinEndpoint.joinUserToLeague(userRecord.getEmail(), invitationCode);
      membersIds.add(userRecord.getUid());
    }

    var banRequest = BaseMembershipRequest.builder().profiles(membersIds).build();
    var response = BanEndpoint.banUsers(leagueId, banRequest, email);
    currentTestResponse.set(response);

    response.then().assertThat().statusCode(HttpStatus.SC_OK);
    membersIds.forEach(memberId -> response.then().assertThat().body("data", hasItem(memberId)));
  }
}
