package com.fansunited.automation.leaguesapi.membership.get;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.core.apis.leagueapi.leagues.CreateLeagueEndpoint.createLeague;
import static com.fansunited.automation.core.apis.leagueapi.membership.GetInvitationsEndpoint.getInvitations;
import static com.fansunited.automation.model.leaguesapi.enums.LeagueType.PRIVATE;
import static org.hamcrest.Matchers.hasItem;

import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.base.leaguesapi.LeagueBaseTest;
import com.fansunited.automation.model.leaguesapi.enums.LeagueType;
import com.fansunited.automation.model.leaguesapi.response.LeagueData;
import com.google.firebase.auth.FirebaseAuthException;
import java.io.IOException;
import java.util.Collections;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName("League - Membership Api - GET /v1/membership/invitations endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class GetLeagueInvitesTest extends LeagueBaseTest {
  @ParameterizedTest(
      name =
          "Show the leagues to which a user has been invited but has not yet accepted, for league type: {arguments}")
  @Tag(REGRESSION)
  @EnumSource(
      value = LeagueType.class,
      mode = EnumSource.Mode.MATCH_ANY,
      names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void getLeaguesInvitations(LeagueType leagueType)
      throws IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException,
          HttpException {

    String email = getCurrentTestUser().getEmail();
    var request = leagueRequest;
    request.setAdministrators(Collections.singletonList(getCurrentTestUser().getUid()));

    var user = createUser();

    if (leagueType != PRIVATE) {
      request.setType(leagueType.getValue());
    }
    var leagueData = createLeague(request, null, email).as(LeagueData.class).getLeague();
    var inviteResponse = inviteUser(user, leagueData.getId(), email);

    inviteResponse.then().statusCode(HttpStatus.SC_OK);

    var getInvitationResponse = getInvitations(leagueType.getValue(), user.getEmail());
    currentTestResponse.set(getInvitationResponse);

    getInvitationResponse
        .then()
        .statusCode(HttpStatus.SC_OK)
        .assertThat()
        .body("data[0].invites", hasItem(user.getUid()));
  }
}
