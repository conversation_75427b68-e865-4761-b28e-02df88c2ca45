package com.fansunited.automation.leaguesapi.membership.post;

import static com.fansunited.automation.constants.ApiConstants.LeaguesApi.LEAGUE_MEMBERS;
import static com.fansunited.automation.core.apis.leagueapi.leagues.CreateLeagueEndpoint.createLeague;
import static com.fansunited.automation.validators.LeaguesApiValidator.validateMembershipResponse;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.not;

import com.fansunited.automation.constants.RegexConstants;
import com.fansunited.automation.core.apis.leagueapi.leagues.LeagueEndpoint;
import com.fansunited.automation.core.apis.leagueapi.membership.AcceptEndpoint;
import com.fansunited.automation.core.base.leaguesapi.LeagueBaseTest;
import com.fansunited.automation.model.leaguesapi.enums.UserInvitesChoice;
import com.fansunited.automation.model.leaguesapi.response.LeagueData;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

@DisplayName(
    "League - Membership Api - POST /v1/membership/accept/{league_id} endpoint happy path tests")
@Execution(ExecutionMode.SAME_THREAD)
public class AcceptMembershipTest extends LeagueBaseTest {

  @ParameterizedTest(
      name = "Verify that users can accept or reject invitation. User choice: {arguments}")
  @EnumSource(
      value = UserInvitesChoice.class,
      mode = EnumSource.Mode.MATCH_ANY,
      names = RegexConstants.NOT_CONTAINS_WORD_INVALID)
  public void usersAcceptInvitation(UserInvitesChoice choice)
      throws IOException,
          ExecutionException,
          FirebaseAuthException,
          InterruptedException,
          HttpException {
    var request = leagueRequest;
    String email = getCurrentTestUser().getEmail();
    var league = createLeague(request, null, email).as(LeagueData.class);
    String leagueId = league.getLeague().getId();

    UserRecord user = createUser();
    inviteUsers(List.of(user.getUid()), leagueId, email);

    var response = AcceptEndpoint.acceptInvitation(choice.getValue(), leagueId, user.getEmail());
    currentTestResponse.set(response);
    validateMembershipResponse(response, leagueId, user.getUid());

    response = LeagueEndpoint.getLeagueById(leagueId, email);
    currentTestResponse.set(response);
    response.then().statusCode(HttpStatus.SC_OK);

    if (choice.equals(UserInvitesChoice.ACCEPT)) {
      response.then().assertThat().body("data." + LEAGUE_MEMBERS, hasItem(user.getUid()));
    } else {
      response.then().assertThat().body("data." + LEAGUE_MEMBERS, not(hasItem(user.getUid())));
    }
  }
}
