package com.fansunited.automation.voting.preferences.put;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.profileapi.ProfileEndpoint.getCurrentTestUserProfileRequest;
import static com.fansunited.automation.core.apis.voting.poll.VoteForAPollEndpoint.voteForeAPoll;
import static org.hamcrest.Matchers.hasItems;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.voting.entities.PollPreference;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.voting.poll.Poll;
import com.fansunited.automation.model.voting.poll.request.PollRequest;
import com.fansunited.automation.model.voting.poll.request.VotingForPollOptionRequest;
import io.restassured.http.ContentType;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

@DisplayName(
    "Voting API - GET /v1/polls/{poll_id}/votes endpoint happy path tests - Poll Options User Preferences Connection Tests")
public class PollOptionsUserPreferencesConnectionTests extends VotingApiPollBaseTest {

  @Test
  @DisplayName("Verify poll options user preferences connection works")
  public void pollOptionsUserPreferencesConnectionTest() throws HttpException {
    var pollRequest = PollRequest.createPollRequestWithOptionsAndPreferencesWithRandomData();
    var poll = createCustomPoll(pollRequest).as(Poll.class).getData();

    // Get the preference IDs from the poll options
    List<String> preferenceIds =
        poll.getOptions().get(0).getPreferencesMapping().stream()
            .map(PollPreference::getId)
            .toList();

    var voteRequest =
        VotingForPollOptionRequest.builder()
            .optionIds(List.of(poll.getOptions().get(0).getId()))
            .build();

    // Vote for the poll option
    voteForeAPoll(
            poll.getId(),
            voteRequest,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail())
        .then()
        .assertThat()
        .statusCode(200);

    // Verify the user preferences are connected to the poll option
    var response = getCurrentTestUserProfileRequest();
    currentTestResponse.set(response);
    // TODO: Add verification once we have a way to query user preferences
    response.then().assertThat().statusCode(200).body("data.preferences", hasItems(preferenceIds.toArray()));

  }
}
