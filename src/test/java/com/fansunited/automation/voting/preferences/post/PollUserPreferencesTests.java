package com.fansunited.automation.voting.preferences.post;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static org.assertj.core.api.Assertions.assertThat;

import com.fansunited.automation.core.apis.voting.entities.PollPreference;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.model.voting.poll.Poll;
import com.fansunited.automation.model.voting.poll.request.PollRequest;
import org.apache.http.HttpException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName(
    "Voting API - POST /v1/polls/ Create poll endpoint happy path tests - Poll Options User Preferences Connection Tests")
public class PollUserPreferencesTests extends VotingApiPollBaseTest {

  private static PollRequest request;
  private static Poll testPoll;

  @BeforeAll
  public static void setUp() throws HttpException {
    request = PollRequest.createPollRequestWithOptionsAndPreferencesWithRandomData();
    testPoll = createCustomPoll(request).as(Poll.class);
  }

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify preferences mapping from request matches response")
  public void verifyPreferencesMappingRequestResponseMatchTest() {
    var responseData = testPoll.getData();

    // Get preferences mapping from request
    var requestOptionsWithPreferences =
        request.getOptions().stream()
            .filter(
                option ->
                    option.getPreferencesMapping() != null
                        && !option.getPreferencesMapping().isEmpty())
            .toList();

    // Get preferences mapping from response
    var responseOptionsWithPreferences =
        responseData.getOptions().stream()
            .filter(
                option ->
                    option.getPreferencesMapping() != null
                        && !option.getPreferencesMapping().isEmpty())
            .toList();

    // Verify both have the same number of options with preferences
    assertThat(responseOptionsWithPreferences).hasSameSizeAs(requestOptionsWithPreferences);

    // Compare preferences mapping for each option
    for (int i = 0; i < requestOptionsWithPreferences.size(); i++) {
      var requestOption = requestOptionsWithPreferences.get(i);
      var responseOption = responseOptionsWithPreferences.get(i);

      // Extract preference IDs from request and response
      var requestPreferenceIds =
          requestOption.getPreferencesMapping().stream()
              .map(PollPreference::getPreferenceId)
              .toList();

      var responsePreferenceIds =
          responseOption.getPreferencesMapping().stream()
              .map(PollPreference::getPreferenceId)
              .toList();

      // Verify preferences mapping matches
      assertThat(responsePreferenceIds).containsExactlyInAnyOrderElementsOf(requestPreferenceIds);
    }
  }
}
