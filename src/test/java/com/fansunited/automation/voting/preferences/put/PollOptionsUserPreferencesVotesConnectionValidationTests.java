package com.fansunited.automation.voting.preferences.put;

import static com.fansunited.automation.constants.TestGroups.REGRESSION;
import static com.fansunited.automation.constants.TestGroups.SMOKE;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.core.apis.voting.poll.CreateVotingEndpoint.createPoll;
import static com.fansunited.automation.core.apis.voting.poll.VoteForAPollEndpoint.voteForeAPoll;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;
import static com.fansunited.automation.model.voting.poll.request.PollRequest.createPollRequestWithOptionsAndPreferencesWithRandomData;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.voting.entities.PollPreference;
import com.fansunited.automation.core.base.voting.VotingApiPollBaseTest;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.voting.poll.Poll;
import com.fansunited.automation.model.voting.poll.request.VotingForPollOptionRequest;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.ArrayList;
import java.util.List;
import org.apache.http.HttpException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Tags;
import org.junit.jupiter.api.Test;

@DisplayName(
    "Voting API - PUT /v1/polls/{poll_id}/votes VALIDATION tests - Poll Options User Preferences Connection Tests")
public class PollOptionsUserPreferencesVotesConnectionValidationTests
    extends VotingApiPollBaseTest {

  @Test
  @Tags({@Tag(REGRESSION), @Tag(SMOKE)})
  @DisplayName("Verify the user preferences limit is 3000")
  public void verifyUserPreferencesLimit() throws HttpException {
    var request = createPollRequestWithOptionsAndPreferencesWithRandomData();
    request.getOptions().get(2).setPreferencesMapping(new ArrayList<>());

    var faker = new Faker();
    for (int i = 0; i < 3002; i++) {
      request
          .getOptions()
          .get(2)
          .getPreferencesMapping()
          .add(PollPreference.builder().preferenceId(faker.lorem().word() + "_" + i).build());
    }

    var poll =
        createPoll(
                request,
                CLIENT_AUTOMATION_ID,
                AuthConstants.ENDPOINTS_API_KEY,
                ContentType.JSON,
                FANS_UNITED_CLIENTS,
                null)
            .as(Poll.class)
            .getData();

    var voteRequest =
        VotingForPollOptionRequest.builder()
            .optionIds(List.of(poll.getOptions().get(2).getId()))
            .build();

    // Vote for the poll option
    voteForeAPoll(
            poll.getId(),
            voteRequest,
            CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON,
            FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail())
        .then()
        .assertThat()
        .statusCode(200);
  }
}
