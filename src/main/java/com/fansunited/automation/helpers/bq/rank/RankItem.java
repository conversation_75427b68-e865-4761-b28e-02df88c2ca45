
package com.fansunited.automation.helpers.bq.rank;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Generated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "correct",
    "points",
    "date_finished",
    "market",
    "game_id",
    "prediction_type",
    "competition_id",
    "away_team_id",
    "match_id",
    "profile_id",
    "game_type",
    "prediction_id",
    "id",
    "timestamp",
    "home_team_id",
    "prediction_last_updated"
})
@Generated("jsonschema2pojo")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RankItem {

    @JsonProperty("correct")
    private String correct;
    @JsonProperty("points")
    private String points;
    @JsonProperty("date_finished")
    private String dateFinished;
    @JsonProperty("market")
    private String market;
    @JsonProperty("game_id")
    private String gameId;
    @JsonProperty("competition_id")
    private String competitionId;
    @JsonProperty("away_team_id")
    private String awayTeamId;
    @JsonProperty("match_id")
    private String matchId;
    @JsonProperty("profile_id")
    private String profileId;
    @JsonProperty("game_type")
    private String gameType;

    @JsonProperty("prediction_id")
    private String predictionId;
    @JsonProperty("id")
    private String id;
    @JsonProperty("timestamp")
    private String timestamp;
    @JsonProperty("home_team_id")
    private String homeTeamId;

    @JsonProperty("golden_goal_minute")
    private Integer goldenGoalMinute;
    @JsonProperty("first_goal_minute")
    private String firstGoalMinute;
    @JsonProperty("prediction_last_updated")
    //@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "UTC")
    private String predictionLastUpdated;

    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("correct")
    public String getCorrect() {
        return correct;
    }

    @JsonProperty("correct")
    public void setCorrect(String correct) {
        this.correct = correct;
    }

    public RankItem withCorrect(String correct) {
        this.correct = correct;
        return this;
    }

    @JsonProperty("points")
    public String getPoints() {
        return points;
    }

    @JsonProperty("points")
    public void setPoints(String points) {
        this.points = points;
    }

    public RankItem withPoints(String points) {
        this.points = points;
        return this;
    }

    @JsonProperty("date_finished")
    public String getDateFinished() {
        return dateFinished;
    }

    @JsonProperty("date_finished")
    public void setDateFinished(String dateFinished) {
        this.dateFinished = dateFinished;
    }

    public RankItem withDateFinished(String dateFinished) {
        this.dateFinished = dateFinished;
        return this;
    }

    @JsonProperty("market")
    public String getMarket() {
        return market;
    }

    @JsonProperty("market")
    public void setMarket(String market) {
        this.market = market;
    }

    public RankItem withMarket(String market) {
        this.market = market;
        return this;
    }

    @JsonProperty("game_id")
    public String getGameId() {
        return gameId;
    }

    @JsonProperty("game_id")
    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public RankItem withGameId(String gameId) {
        this.gameId = gameId;
        return this;
    }

    @JsonProperty("competition_id")
    public String getCompetitionId() {
        return competitionId;
    }

    @JsonProperty("competition_id")
    public void setCompetitionId(String competitionId) {
        this.competitionId = competitionId;
    }

    public RankItem withCompetitionId(String competitionId) {
        this.competitionId = competitionId;
        return this;
    }

    @JsonProperty("away_team_id")
    public String getAwayTeamId() {
        return awayTeamId;
    }

    @JsonProperty("away_team_id")
    public void setAwayTeamId(String awayTeamId) {
        this.awayTeamId = awayTeamId;
    }

    public RankItem withAwayTeamId(String awayTeamId) {
        this.awayTeamId = awayTeamId;
        return this;
    }

    @JsonProperty("match_id")
    public String getMatchId() {
        return matchId;
    }

    @JsonProperty("match_id")
    public void setMatchId(String matchId) {
        this.matchId = matchId;
    }

    public RankItem withMatchId(String matchId) {
        this.matchId = matchId;
        return this;
    }

    @JsonProperty("profile_id")
    public String getProfileId() {
        return profileId;
    }

    @JsonProperty("profile_id")
    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public RankItem withProfileId(String profileId) {
        this.profileId = profileId;
        return this;
    }

    @JsonProperty("game_type")
    public String getGameType() {
        return gameType;
    }

    @JsonProperty("game_type")
    public void setGameType(String gameType) {
        this.gameType = gameType;
    }

    public RankItem withGameType(String gameType) {
        this.gameType = gameType;
        return this;
    }

    @JsonProperty("prediction_id")
    public String getPredictionId() {
        return predictionId;
    }

    @JsonProperty("prediction_id")
    public void setPredictionId(String predictionId) {
        this.predictionId = predictionId;
    }

    public RankItem withPredictionId(String predictionId) {
        this.predictionId = predictionId;
        return this;
    }

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(String id) {
        this.id = id;
    }

    public RankItem withId(String id) {
        this.id = id;
        return this;
    }

    @JsonProperty("timestamp")
    public String getTimestamp() {
        return timestamp;
    }

    @JsonProperty("timestamp")
    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public RankItem withTimestamp(String timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    @JsonProperty("home_team_id")
    public String getHomeTeamId() {
        return homeTeamId;
    }

    @JsonProperty("home_team_id")
    public void setHomeTeamId(String homeTeamId) {
        this.homeTeamId = homeTeamId;
    }

    public RankItem withHomeTeamId(String homeTeamId) {
        this.homeTeamId = homeTeamId;
        return this;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    public RankItem withAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
        return this;
    }

}
