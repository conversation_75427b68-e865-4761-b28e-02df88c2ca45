
package com.fansunited.automation.helpers.bq.events;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "content",
    "tags",
    "campaign"
})
@Generated("jsonschema2pojo")
public class Data {

    @JsonProperty("content")
    private Content content;
    @JsonProperty("tags")
    private List<Tag> tags = null;
    @JsonProperty("campaign")
    private Campaign campaign;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("content")
    public Content getContent() {
        return content;
    }

    @JsonProperty("content")
    public void setContent(Content content) {
        this.content = content;
    }

    public Data withContent(Content content) {
        this.content = content;
        return this;
    }

    @JsonProperty("tags")
    public List<Tag> getTags() {
        return tags;
    }

    @JsonProperty("tags")
    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    public Data withTags(List<Tag> tags) {
        this.tags = tags;
        return this;
    }

    @JsonProperty("campaign")
    public Campaign getCampaign() {
        return campaign;
    }

    @JsonProperty("campaign")
    public void setCampaign(Campaign campaign) {
        this.campaign = campaign;
    }

    public Data withCampaign(Campaign campaign) {
        this.campaign = campaign;
        return this;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    public Data withAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
        return this;
    }

}
