package com.fansunited.automation.helpers.bq.excluded;

import static com.fansunited.automation.mappers.EventMapper.EXCLUDED_TABLE;

import com.fansunited.automation.helpers.bq.BaseDataPump;
import com.fansunited.automation.mappers.EventMapper;
import com.google.cloud.bigquery.BigQuery;
import java.util.List;

public class ExcludedDataPump extends BaseDataPump {

  private final BigQuery bigQuery;
  private final EventMapper eventMapper = new EventMapper();

  public ExcludedDataPump(BigQuery bigQuery) {
    this.bigQuery = bigQuery;
  }

  public void insertSingleExcludedItem(String excludedProfileId) throws Exception {
    var excludedItem = eventMapper.excludedItem(excludedProfileId);
    insertJson(List.of(excludedItem), bigQuery, EXCLUDED_TABLE);
  }
}
