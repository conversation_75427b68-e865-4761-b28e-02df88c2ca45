package com.fansunited.automation.helpers;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.profileapi.leads.CreateLeadEndpoint;
import com.fansunited.automation.model.profileapi.leades.CreatLeadResponse;
import com.fansunited.automation.model.profileapi.leades.CreateLeadRequest;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.http.HttpException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LeadGenerator {
  private static final Logger LOG = LoggerFactory.getLogger(LeadGenerator.class);

  private LeadGenerator() {}

  public static CreateLeadRequest generateRandomCreateLeadRequest() {
    Faker faker = new Faker();
    Map<String, String> customField = new HashMap<>();

    customField.put(faker.lorem().word(), faker.lorem().word());
    customField.put(faker.lorem().word(), faker.lorem().word());

    return CreateLeadRequest.builder()
        .campaignName(faker.name().title())
        .campaignId(UUID.randomUUID().toString())
        .contentName(faker.lorem().word())
        .contentId(UUID.randomUUID().toString())
        .contentType(faker.lorem().word())
        .country(faker.country().name())
        .fullName(faker.name().fullName())
        .firstName(faker.name().firstName())
        .lastName(faker.name().lastName())
        .email(faker.internet().emailAddress())
        .gender(faker.demographic().sex())
        .phoneCountryCode(faker.address().countryCode())
        .phoneNumber(faker.phoneNumber().phoneNumber())
        .customFields(customField)
        .build();
  }

  /**
   * Generates multiple leads with either same or random data for each lead.
   *
   * @param numberOfLeads number of leads to generate
   * @param useSameData if true, uses the same data for all leads; if false, generates random data
   *     for each
   * @return List of created lead responses
   */
  public static List<CreatLeadResponse> generateLeads(int numberOfLeads, boolean useSameData) {
    List<CreatLeadResponse> leads = new ArrayList<>();
    CreateLeadRequest request = useSameData ? generateRandomCreateLeadRequest() : null;

    try {
      for (int i = 0; i < numberOfLeads; i++) {
        CreateLeadRequest currentRequest =
            useSameData ? request : generateRandomCreateLeadRequest();
        var response = createSingleLead(currentRequest);
        leads.add(response.as(CreatLeadResponse.class));
      }
    } catch (Exception e) {
      LOG.error("Unexpected error while generating leads: {}", e.getMessage());
      try {
        throw new HttpException("Failed to generate leads: " + e.getMessage());
      } catch (HttpException ex) {
        throw new RuntimeException(ex);
      }
    }
    return leads;
  }

  public static Response createSingleLead(CreateLeadRequest request) {
    try {
      return CreateLeadEndpoint.createLead(
          null,
          request,
          CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON,
          true,
          null);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
