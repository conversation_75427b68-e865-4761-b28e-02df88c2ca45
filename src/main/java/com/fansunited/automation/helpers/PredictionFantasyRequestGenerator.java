package com.fansunited.automation.helpers;

import static com.fansunited.automation.model.predictionapi.games.enums.MatchType.FOOTBALL;
import static com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket.FantasyMarkets.PLAYER_PERFORMANCE;

import com.fansunited.automation.core.apis.clientapi.enums.PlayerPerformanceRole;
import com.fansunited.automation.model.loyaltyapi.templates.response.TemplateResponse;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerPerformancePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.request.CreateFantasyPredictionRequest;
import java.util.List;
import lombok.Data;

@Data
public class PredictionFantasyRequestGenerator {
  private PredictionFantasyRequestGenerator() {}

  public static CreateFantasyPredictionRequest getFantasyPredictionRequest(
      TemplateResponse template,
      String keeperId,
      String defenderId,
      String forwardId,
      String midfielderId,
      String captain,
      String viceCaptain) {
    return CreateFantasyPredictionRequest.builder()
        .template_id(template.getId())
        .group_id(template.getGroups().get(0).getGroupId())
        .fixtures(
            List.of(
                PlayerPerformancePredictionFixture.builder()
                    .market(PLAYER_PERFORMANCE.name())
                    .player_id(keeperId)
                    .match_type(FOOTBALL.getValue())
                    .build(),
                PlayerPerformancePredictionFixture.builder()
                    .market(PLAYER_PERFORMANCE.name())
                    .player_id(defenderId)
                    .match_type(FOOTBALL.getValue())
                    .build(),
                PlayerPerformancePredictionFixture.builder()
                    .market(PLAYER_PERFORMANCE.name())
                    .player_id(forwardId)
                    .match_type(FOOTBALL.getValue())
                    .build(),
                PlayerPerformancePredictionFixture.builder()
                    .market(PLAYER_PERFORMANCE.name())
                    .player_id(midfielderId)
                    .match_type(FOOTBALL.getValue())
                    .build(),
                PlayerPerformancePredictionFixture.builder()
                    .market(PLAYER_PERFORMANCE.name())
                    .player_id(captain)
                    .match_type(FOOTBALL.getValue())
                    .role(PlayerPerformanceRole.VICE_CAPTAIN)
                    .build(),
                PlayerPerformancePredictionFixture.builder()
                    .market(PLAYER_PERFORMANCE.name())
                    .player_id(viceCaptain)
                    .match_type(FOOTBALL.getValue())
                    .role(PlayerPerformanceRole.CAPTAIN)
                    .build()
                ))
        .build();
  }
}
