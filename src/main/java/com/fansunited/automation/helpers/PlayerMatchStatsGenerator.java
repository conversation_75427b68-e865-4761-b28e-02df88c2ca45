package com.fansunited.automation.helpers;


import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getRandomPlayerFromMatchByPosition;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.model.footballapi.players.enums.PlayerPosition.DEFENDER;
import static com.fansunited.automation.model.footballapi.players.enums.PlayerPosition.FORWARD;
import static com.fansunited.automation.model.footballapi.players.enums.PlayerPosition.KEEPER;
import static com.fansunited.automation.model.footballapi.players.enums.PlayerPosition.MIDFIELDER;

import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.core.resolver.hibernate.PlayerMatchStats;
import com.fansunited.automation.core.resolver.hibernate.PlayerStatsId;
import com.fansunited.automation.model.footballapi.players.enums.PlayerPosition;
import java.util.List;

public class PlayerMatchStatsGenerator {
    private PlayerMatchStatsGenerator() {}

    public static PlayerMatchStats generateRandomPlayerMatchStats(String playerId, String eventId) {
    return PlayerMatchStats.builder()
        .id(new PlayerStatsId(playerId,eventId))
        .minutesPlayed(generateRandomNumber(0, 90))
        .yellowCards(generateRandomNumber(0, 2))
        .redCards(generateRandomNumber(0, 1))
        .goals(generateRandomNumber(0, 5))
        .penaltyGoals(generateRandomNumber(0, 2))
        .penaltyCommitted(generateRandomNumber(0, 2))
        .penaltyWon(generateRandomNumber(0, 2))
        .penaltyMissed(generateRandomNumber(0, 2))
        .ownGoals(generateRandomNumber(0, 2))
        .assists(generateRandomNumber(0, 6))
        .shots(generateRandomNumber(0, 2))
        .shotsOn(generateRandomNumber(0, 15))
        .offsides(generateRandomNumber(0, 5))
        .foulsCommitted(generateRandomNumber(0, 8))
        .tackles(generateRandomNumber(0, 8))
        .caughtBall(generateRandomNumber(0, 0))
        .saves(generateRandomNumber(0, 8))
        .build();
    }

    public static List<String> generatePlayerIds(Match match) {
        return List.of(
            getRandomPlayerFromMatchByPosition(match, false, KEEPER.getValue()),
            getRandomPlayerFromMatchByPosition(match, true, DEFENDER.getValue()),
            getRandomPlayerFromMatchByPosition(match, true, FORWARD.getValue()),
            getRandomPlayerFromMatchByPosition(match, false, MIDFIELDER.getValue()));
    }

    public static String generatePlayerId(Match match, PlayerPosition playerPosition) {
       return getRandomPlayerFromMatchByPosition(match, true, playerPosition.getValue());
    }
}
