package com.fansunited.automation.helpers;

import static com.fansunited.automation.constants.ApiConstants.ApiIdConstants.LOYALTY_API_ID;
import static com.fansunited.automation.constants.ApiConstants.ApiIdConstants.PREDICTION_GAMES_API_ID;
import static com.fansunited.automation.constants.ApiConstants.ApiIdConstants.PROFILE_API_ID;

import com.fansunited.automation.core.apis.clientapi.enums.TtlEndpoints;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class ApiNameMapper {

  private static final Map<String, String> API_NAMES = new HashMap<>();

  static {

    TtlEndpoints.getTtlPredictionsEndpointsId()
        .stream()
        .map(s -> API_NAMES.put(String.valueOf(s), PREDICTION_GAMES_API_ID))
        .collect(Collectors.toList());
    TtlEndpoints.getTtlLoyaltyEndpointsId()
        .stream()
        .map(s -> API_NAMES.put(String.valueOf(s), LOYALTY_API_ID))
        .collect(Collectors.toList());
    TtlEndpoints.getTtlProfileEndpointsId()
        .stream()
        .map(s -> API_NAMES.put(String.valueOf(s), PROFILE_API_ID))
        .collect(Collectors.toList());
  }

  public static String getApiName(String value) {
    return API_NAMES.get(value);
  }
}
