package com.fansunited.automation.helpers;

import static com.fansunited.automation.core.resolver.enums.TimelineEventType.GOAL;

import com.fansunited.automation.core.resolver.enums.TimelineMatchInputValues;
import com.fansunited.automation.core.resolver.hibernate.PlayerMatchStats;
import com.fansunited.automation.model.clientapi.features.response.AssistsExtended;
import com.fansunited.automation.model.clientapi.features.response.Coefficients;
import com.fansunited.automation.model.clientapi.features.response.ConcededGoalsExtended;
import com.fansunited.automation.model.clientapi.features.response.FoulsCommittedExtended;
import com.fansunited.automation.model.clientapi.features.response.SavesExtended;
import com.fansunited.automation.model.clientapi.features.response.ShotsOnExtended;
import com.fansunited.automation.model.clientapi.features.response.TacklesExtended;
import com.fansunited.automation.model.footballapi.players.enums.PlayerPosition;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
public class FootballFantasyCalculatePoints {
  private static final Logger logger =
      LoggerFactory.getLogger(FootballFantasyCalculatePoints.class);
  private int totalPoints = 0;

  private Integer assists;
  private Integer shots;
  private Integer offsides;
  private Integer tackles;
  private Integer saves;
  private Integer minutesPlayed;
  private Integer yellowCards;
  private Integer redCards;
  private Integer goalsGoalkeeper;
  private Integer goalsDefender;
  private Integer goalsMidfielder;
  private Integer goalsForwards;
  private Integer goals;
  private Integer penaltyGoals;
  private Integer ownGoals;
  private Integer penaltyCommitted;
  private Integer penaltyWon;
  private Integer penaltyMissed;
  private Integer penaltySaved;
  private Integer penaltyReceived;
  private Integer caughtBall;
  private Integer shotsOn;
  private Integer foulsCommitted;

  private Integer captain;
  private Integer viceCaptain;

  public int calculatePoints(
      PlayerMatchStats playerMatchStats, Coefficients coefficients, PlayerPosition playerPosition) {

    setValues(playerMatchStats);

    // Regular Points
    totalPoints += calculateRegularPoints(playerMatchStats, coefficients, playerPosition);

    // Extended Points
    if ((shotsOn != null) && (shotsOn > 0) && (coefficients.getShotsOnExtended() != null)) {
      totalPoints += getShotsOnExtendedPoints(shotsOn, coefficients.getShotsOnExtended());
    }

    if ((saves != null) && (saves > 0) && (coefficients.getSavesExtended() != null)) {
      totalPoints += getSavesExtendedPoints(saves, coefficients.getSavesExtended());
    }

    if ((assists != null) && (assists > 0) && (coefficients.getAssistsExtended() != null)) {
      totalPoints += getAssistsExtendedPoints(assists, coefficients.getAssistsExtended());
    }

    if ((tackles != null) && (tackles > 0) && (coefficients.getTacklesExtended() != null)) {
      totalPoints += getTacklesExtendedPoints(tackles, coefficients.getTacklesExtended());
    }

    if ((foulsCommitted != null)
        && (foulsCommitted > 0)
        && (coefficients.getFoulsCommittedExtended() != null)) {
      totalPoints +=
          getFoulsCommittedExtendedPoints(foulsCommitted, coefficients.getFoulsCommittedExtended());
    }

    if ((playerMatchStats.getConcededGoals() != null)
        && (playerMatchStats.getConcededGoals() > 0)
        && (coefficients.getConcededGoalsExtended() != null)) {
      totalPoints +=
          getConcededGoalsExtendedPoints(
              playerMatchStats.getConcededGoals(), coefficients.getConcededGoalsExtended());
    }

    return totalPoints;
  }

  private void setValues(PlayerMatchStats playerMatchStats) {
    Map<String, Object> fieldValues = extractFieldValues(playerMatchStats);
    populateFields(fieldValues);
  }

  private Map<String, Object> extractFieldValues(PlayerMatchStats playerMatchStats) {
    Map<String, Object> fieldValues = new HashMap<>();
    Field[] fields = playerMatchStats.getClass().getDeclaredFields();

    for (Field field : fields) {
      field.setAccessible(true);
      try {
        Object value = field.get(playerMatchStats);
        fieldValues.put(field.getName(), value);
      } catch (IllegalAccessException e) {
        logger.error("An ERROR occurred ", e);
      }
    }
    return fieldValues;
  }

  private void populateFields(Map<String, Object> fieldValues) {
    fieldValues.forEach(
        (fieldName, fieldValue) -> {
          try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            if (field.getType().isAssignableFrom(fieldValue.getClass())) {
              field.set(this, fieldValue);
            } else {
              logger.warn("Type mismatch for field: {}", fieldName);
            }
          } catch (NoSuchFieldException e) {
            logger.error("Field not found: {}", fieldName);
          } catch (IllegalAccessException e) {
            logger.error("Cannot access field: {}", fieldName);
          }
        });
  }

  private int calculateRegularPoints(
      PlayerMatchStats playerMatchStats, Coefficients coefficients, PlayerPosition playerPosition) {

    int points = 0;

    points +=
        (playerMatchStats.getMinutesPlayed() != null && playerMatchStats.getMinutesPlayed() >= 60)
            ? coefficients.getMinutesEqualOrOver60()
            : coefficients.getMinutesUnder60();

    points +=
        (playerMatchStats.getYellowCards() != null ? playerMatchStats.getYellowCards() : 0)
            * coefficients.getYellowCards();
    points +=
        (playerMatchStats.getRedCards() != null ? playerMatchStats.getRedCards() : 0)
            * coefficients.getRedCards();
    points += getPointsGoals(playerPosition, playerMatchStats, coefficients);
    points +=
        (playerMatchStats.getPenaltyGoals() != null ? playerMatchStats.getPenaltyGoals() : 0)
            * coefficients.getPenaltyGoals();
    points +=
        (playerMatchStats.getPenaltyCommitted() != null
                ? playerMatchStats.getPenaltyCommitted()
                : 0)
            * coefficients.getPenaltyCommitted();
    points +=
        (playerMatchStats.getPenaltyWon() != null ? playerMatchStats.getPenaltyWon() : 0)
            * coefficients.getPenaltyWon();
    points +=
        (playerMatchStats.getPenaltyMissed() != null ? playerMatchStats.getPenaltyMissed() : 0)
            * coefficients.getPenaltyMissed();
    points +=
        (playerMatchStats.getOwnGoals() != null ? playerMatchStats.getOwnGoals() : 0)
            * coefficients.getOwnGoals();
    points +=
        (playerMatchStats.getAssists() != null ? playerMatchStats.getAssists() : 0)
            * coefficients.getAssists();
    points += getPointsCleanSheets(playerPosition, playerMatchStats, coefficients);
    points +=
        (playerMatchStats.getShots() != null ? playerMatchStats.getShots() : 0)
            * coefficients.getShots();
    points +=
        (playerMatchStats.getShotsOn() != null ? playerMatchStats.getShotsOn() : 0)
            * coefficients.getShotsOn();
    points +=
        (playerMatchStats.getOffsides() != null ? playerMatchStats.getOffsides() : 0)
            * coefficients.getOffsides();
    points +=
        (playerMatchStats.getFoulsCommitted() != null ? playerMatchStats.getFoulsCommitted() : 0)
            * coefficients.getFoulsCommitted();
    points +=
        (playerMatchStats.getTackles() != null ? playerMatchStats.getTackles() : 0)
            * coefficients.getTackles();
    points +=
        (playerMatchStats.getConcededGoals() != null ? playerMatchStats.getConcededGoals() : 0)
            * coefficients.getConcededGoals();
    points +=
        (playerMatchStats.getCaughtBall() != null ? playerMatchStats.getCaughtBall() : 0)
            * coefficients.getCaughtBall();
    points +=
        (playerMatchStats.getSaves() != null ? playerMatchStats.getSaves() : 0)
            * coefficients.getSaves();

    return points;
  }

  private int getShotsOnExtendedPoints(int shotsOn, ShotsOnExtended shotsOnExtended) {
    return switch (shotsOn) {
      case 1 -> shotsOnExtended.getOneShotOn();
      case 2 -> shotsOnExtended.getTwoShotsOn();
      case 3 -> shotsOnExtended.getThreeShotsOn();
      case 4 -> shotsOnExtended.getFourShotsOn();
      case 5 -> shotsOnExtended.getFiveShotsOn();
      case 6 -> shotsOnExtended.getSixShotsOn();
      case 7 -> shotsOnExtended.getSevenShotsOn();
      case 8 -> shotsOnExtended.getEightShotsOn();
      case 9 -> shotsOnExtended.getNineShotsOn();
      default -> {
        if (shotsOn >= 10) {
          yield shotsOnExtended.getTenShotsOnOrMore();
        } else {
          yield 0;
        }
      }
    };
  }

  private int getSavesExtendedPoints(int saves, SavesExtended savesExtended) {
    return switch (saves) {
      case 1 -> savesExtended.getOneSave();
      case 2 -> savesExtended.getTwoSaves();
      case 3 -> savesExtended.getThreeSaves();
      case 4 -> savesExtended.getFourSaves();
      case 5 -> savesExtended.getFiveSaves();
      default -> {
        if (saves >= 6) {
          yield savesExtended.getSixSavesOrMore();
        } else {
          yield 0;
        }
      }
    };
  }

  private int getAssistsExtendedPoints(int assists, AssistsExtended assistsExtended) {
    return switch (assists) {
      case 1 -> assistsExtended.getOneAssists();
      case 2 -> assistsExtended.getTwoAssists();
      case 3 -> assistsExtended.getThreeAssists();
      default -> {
        if (assists >= 4) {
          yield assistsExtended.getFourAssistsOrMore();
        } else {
          yield 0;
        }
      }
    };
  }

  private int getTacklesExtendedPoints(int tackles, TacklesExtended tacklesExtended) {
    return switch (tackles) {
      case 1 -> tacklesExtended.getOneTackle();
      case 2 -> tacklesExtended.getTwoTackles();
      case 3 -> tacklesExtended.getThreeTackles();
      case 4 -> tacklesExtended.getFourTackles();
      default -> {
        if (tackles >= 5) {
          yield tacklesExtended.getFiveTacklesOrMore();
        } else {
          yield 0;
        }
      }
    };
  }

  private int getConcededGoalsExtendedPoints(
      int concededGoals, ConcededGoalsExtended concededGoalsExtended) {
    return switch (concededGoals) {
      case 1 -> concededGoalsExtended.getOneGoal();
      case 2 -> concededGoalsExtended.getTwoGoals();
      case 3 -> concededGoalsExtended.getThreeGoals();
      case 4 -> concededGoalsExtended.getFourGoals();
      default -> {
        if (concededGoals >= 5) {
          yield concededGoalsExtended.getFiveGoalsOrMore();
        } else {
          yield 0;
        }
      }
    };
  }

  private int getFoulsCommittedExtendedPoints(
      int foulsCommitted, FoulsCommittedExtended foulsCommittedExtended) {
    return switch (foulsCommitted) {
      case 1 -> foulsCommittedExtended.getOneFoulCommitted();
      case 2 -> foulsCommittedExtended.getTwoFoulsCommitted();
      case 3 -> foulsCommittedExtended.getThreeFoulsCommitted();
      case 4 -> foulsCommittedExtended.getFourFoulsCommitted();
      default -> {
        if (foulsCommitted >= 5) {
          yield foulsCommittedExtended.getFiveFoulsCommittedOrMore();
        } else {
          yield 0;
        }
      }
    };
  }

  public int getPointsForConcededGoals(
      boolean isHomeTeam,
      PlayerMatchStats playerMatchStats,
      TimelineMatchInputValues.GenerateMatchTimeLine generateMatchTimeLine,
      Coefficients coefficients) {

    int playedMinutes = playerMatchStats.getMinutesPlayed();
    int eventMinute = generateMatchTimeLine.getMinute();
    String eventType = generateMatchTimeLine.getTimelineEventType().getValue();

    if (eventType.equals(GOAL.getValue())
        && (generateMatchTimeLine.isHomeTeam() == isHomeTeam)
        && eventMinute <= playedMinutes) {

      return coefficients.getConcededGoals();
    }
    return 0;
  }

  private int getPointsCleanSheets(
      PlayerPosition position, PlayerMatchStats stats, Coefficients coefficients) {
    if (position == null) {
      return (stats.getCleanSheets() != null ? stats.getCleanSheets() : 0)
          * coefficients.getCleanSheetsMidfielder();
    }

    int cleanSheets = stats.getCleanSheets() != null ? stats.getCleanSheets() : 0;

    return switch (position) {
      case MIDFIELDER -> cleanSheets * coefficients.getCleanSheetsMidfielder();
      case KEEPER -> cleanSheets * coefficients.getCleanSheetsGoalkeeper();
      case DEFENDER -> cleanSheets * coefficients.getCleanSheetsDefender();
      case FORWARD -> cleanSheets * coefficients.getCleanSheetsForwards();
      case CAPTAIN -> 0;
      case VICE_CAPTAIN -> 0;
    };
  }

  private int getPointsGoals(
      PlayerPosition position, PlayerMatchStats stats, Coefficients coefficients) {
    if (position == null) {
      return (stats.getGoals() != null ? stats.getGoals() : 0) * coefficients.getGoalsMidfielder();
    }

    int goalsInMatch = stats.getGoals() != null ? stats.getGoals() : 0;

    return switch (position) {
      case MIDFIELDER -> goalsInMatch * coefficients.getGoalsMidfielder();
      case KEEPER -> goalsInMatch * coefficients.getGoalsGoalkeeper();
      case DEFENDER -> goalsInMatch * coefficients.getGoalsDefender();
      case FORWARD -> goalsInMatch * coefficients.getGoalsForwards();
      case CAPTAIN -> 3;
      case VICE_CAPTAIN -> 2;
    };
  }
}
