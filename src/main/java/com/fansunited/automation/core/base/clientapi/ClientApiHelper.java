package com.fansunited.automation.core.base.clientapi;

import static com.fansunited.automation.constants.AuthConstants.DEFAULT_AUDIENCE;
import static com.fansunited.automation.constants.AuthConstants.ENDPOINTS_API_KEY;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.core.apis.clientapi.ClientEndpoint;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.github.javafaker.Faker;
import io.restassured.http.ContentType;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.HttpException;
import org.apache.http.HttpStatus;

public class ClientApiHelper {

  public static Map<String, Object> createClients() throws HttpException {

    Map<String, Object> docData = new HashMap<>();
    docData.put("id", new Faker().internet().uuid().replace("-","_"));
    docData.put("name", "Client " + RandomStringUtils.randomAlphanumeric(15));
    List<Map<String, String>> endpointsKeys = List.of(
        Map.of("label", "Web", "value", ENDPOINTS_API_KEY),
        Map.of("label", "Android", "value", "yyyyyyyyy"));
    docData.put("google_endpoints_keys", endpointsKeys);
    docData.put("audiences", List.of(DEFAULT_AUDIENCE));
    docData.put("audience", DEFAULT_AUDIENCE);
    docData.put("infrastructure", "STAGE");
    docData.put("secrets", Map.of("firebase_credentials", "client-stage-firebase-credentials"));
    docData.put("created_at", new Date());
    docData.put("updated_at", new Date());
    docData.put("anonymize_user_data", true);

    var response =
        ClientEndpoint.createClient(FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS, docData,
            AuthConstants.ENDPOINTS_API_KEY,
            ContentType.JSON);

    response
        .then()
        .assertThat()
        .statusCode(HttpStatus.SC_OK);
    return docData;
  }
}
