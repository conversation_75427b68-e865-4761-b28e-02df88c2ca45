package com.fansunited.automation.core.apis.leagueapi;

import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_KEY;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithAuth;
import static com.fansunited.automation.utils.RestAssuredUtils.requestWithoutAuth;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.helpers.ConfigReader;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

public class BaseSetup {
  private static final String baseUri =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.LEAGUES_API_BASE_URL);
  private static final String port =
      ConfigReader.getInstance().getProperty(ConfigReader.PropertyKey.LEAGUES_API_PORT);

  protected static RequestSpecification getRequiredRequestSpec(
      FirebaseHelper.FansUnitedProject project,
      String apiKey,
      String clientId,
      ContentType contentType,
      String authToken,
      String email)
      throws HttpException {

    RequestSpecification requestSpecification;

    if (email == null) {
      if (authToken == null) {
        requestSpecification = requestWithoutAuth(baseUri, port);
      } else {
        requestSpecification = requestWithAuth(authToken, baseUri, port);
      }
    } else {
      requestSpecification =
          requestWithAuth(project, email, AuthConstants.DEFAULT_USER_PASS, baseUri, port);
    }

    if (clientId != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_CLIENT_ID, clientId);
    }

    if (apiKey != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_KEY, apiKey);
    }

    if (contentType != null) {
      requestSpecification = requestSpecification.contentType(contentType);
    }
    return requestSpecification;
  }
}
