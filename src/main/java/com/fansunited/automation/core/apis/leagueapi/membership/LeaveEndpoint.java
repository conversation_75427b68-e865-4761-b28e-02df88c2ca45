package com.fansunited.automation.core.apis.leagueapi.membership;

import static com.fansunited.automation.constants.UrlParamValues.LeaguesApi.PATH_PARAM_LEAGUE_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.apis.leagueapi.BaseSetup;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class LeaveEndpoint extends BaseSetup {
  private static Response leaveLeague(
      String leagueId,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType,
      String authToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(project, apiKey, clientId, contentType, authToken, email);
    return requestSpecification
        .pathParam(PATH_PARAM_LEAGUE_ID, leagueId)
        .when()
        .post(Endpoints.MembershipApi.LEAVE_MEMBER);
  }

  public static Response leaveLeague(String leagueId, String email) {
    return leaveLeague(leagueId, email, AuthConstants.ENDPOINTS_API_KEY);
  }

  public static Response leaveLeague(String leagueId, String email, String apiKey) {
    return leaveLeague(leagueId, email, apiKey, null);
  }

  public static Response leaveLeague(
      String leagueId, String email, String apiKey, String authToken) {
    return leaveLeague(leagueId, email, apiKey, authToken, CLIENT_AUTOMATION_ID);
  }

  public static Response leaveLeague(
      String leagueId, String email, String apiKey, String authToken, String clientId) {
    try {
      return leaveLeague(
          leagueId,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          email,
          clientId,
          apiKey,
          ContentType.JSON,
          authToken);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }

  public static Response leaveLeague(String leagueId, String email, ContentType contentType) {
    try {
      return leaveLeague(
          leagueId,
          FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
          email,
          CLIENT_AUTOMATION_ID,
          AuthConstants.ENDPOINTS_API_KEY,
          contentType,
          null);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
