package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_LIST_ID;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.QUERY_PARAM_START_AFTER;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.clientapi.features.response.managelists.CreateListRequest;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class ManageListsEndpoint extends BaseSetup {

  public static Response createList(
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      Object body,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            1, null, isAuthRequired, project, email, null, null, null, apiKey, contentType);

    return requestSpecification
        .when()
        .body(body)
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .post(Endpoints.ClientApi.CREATE_LIST);
  }

  public static Response getClientLists(
      String startAfter,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            1, null, isAuthRequired, project, email, null, null, null, apiKey, contentType);

    if (startAfter != null) {
      requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }

    return requestSpecification
        .when()
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .get(Endpoints.ClientApi.GET_LISTS);
  }

  public static Response getClientListById(
      String listId,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            1, null, isAuthRequired, project, email, null, null, null, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .pathParam(PATH_PARAM_LIST_ID, listId)
        .get(Endpoints.ClientApi.GET_LIST_BY_ID);
  }

  public static Response deleteClientListById(
      String listId,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            1, null, isAuthRequired, project, email, null, null, null, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .pathParam(PATH_PARAM_LIST_ID, listId)
        .delete(Endpoints.ClientApi.DELETE_LIST_BY_ID);
  }

  public static Response updateClientListById(
      String listId,
      CreateListRequest body,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            1, null, isAuthRequired, project, email, null, null, null, apiKey, contentType);

    return requestSpecification
        .when()
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .pathParam(PATH_PARAM_LIST_ID, listId)
        .body(body)
        .put(Endpoints.ClientApi.UPDATE_LIST_BY_ID);
  }
}
