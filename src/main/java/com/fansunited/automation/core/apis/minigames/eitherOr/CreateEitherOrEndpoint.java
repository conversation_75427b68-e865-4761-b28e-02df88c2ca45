package com.fansunited.automation.core.apis.minigames.eitherOr;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class CreateEitherOrEndpoint extends EitherOrBaseSetup {

  public static Response createEitherOr(Object body,String clientId,
      String apiKey, ContentType contentType, FirebaseHelper.FansUnitedProject project,
      String email)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .when()
        .body(body)
        .post(Endpoints.MiniGamesApi.EITHER_OR);
  }
}
