package com.fansunited.automation.core.apis.voting.entities;

import static com.fansunited.automation.core.apis.voting.entities.Tag.createTagWithRandomData;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Jacksonized
public class Context {
  private Content content;
  private List<Tag> tags;
  private Campaign campaign;

  public static Context createContextWithRandomData() {
    return Context.builder()
        .tags(List.of(createTagWithRandomData(), createTagWithRandomData()))
        .content(Content.createContentWithRandomData())
        .campaign(Campaign.createCampaignWithRandomData())
        .build();
  }
}
