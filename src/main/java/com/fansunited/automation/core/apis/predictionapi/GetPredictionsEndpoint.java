package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.ApiConstants.LoyaltyApi.MATCH_IDS_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.DEFAULT_LIMIT_PARAM_GAME_RESULTS;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.PATH_PARAM_USER_ID;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.QUERY_PARAM_GAME_TYPES;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_START_AFTER;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_STATUS;
import static com.fansunited.automation.core.apis.predictionapi.BaseSetup.getRequiredRequestSpec;
import static com.fansunited.automation.core.base.AuthBase.getCurrentTestUser;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionStatus;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.http.HttpException;

@Data
@Builder
@NoArgsConstructor @AllArgsConstructor
public class GetPredictionsEndpoint {
  private PredictionStatus status;
  private int limit;
  private String startAfter;
  private GameType gameType;
  private String name;
  private List<String> matchIds;


  public Response getOwnPredictions()
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            getCurrentTestUser().getEmail(), CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    if (limit != -1) {
      if(limit == 0){
        limit = DEFAULT_LIMIT_PARAM_GAME_RESULTS;
      }
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }

    if (status != null) {
      requestSpecification = requestSpecification.queryParam(STATUS_PROP, status);
    }

    if (gameType != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_GAME_TYPES, gameType.getValue());
    }

    if (matchIds != null) {
      requestSpecification = requestSpecification.queryParam(MATCH_IDS_PROP, String.join(",", matchIds));
    }

    return requestSpecification
        .when()
        .get(Endpoints.PredictionApi.PREDICTIONS);
  }

  public Response getPredictionsForSpecificUser()
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, null, null, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    if (status != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_STATUS, status);
    }

    if (limit != -1) {
      if(limit == 0){
        limit = DEFAULT_LIMIT_PARAM_GAME_RESULTS;
      }
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }

    if (gameType != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_GAME_TYPES, gameType.getValue());
    }

    if (matchIds != null) {
      requestSpecification = requestSpecification.queryParam(MATCH_IDS_PROP, String.join(",", matchIds));
    }

    return requestSpecification
        .pathParam(PATH_PARAM_USER_ID, getCurrentTestUser().getUid())
        .when()
        .get(Endpoints.PredictionApi.PREDICTIONS_BY_USER_ID);
  }

  /**
   * The method returns all own predictions for a specific user
   * @param userEmail the email of the user
   * @return getOwnPredictions response
   * @throws HttpException
   */
  public Response getOwnPredictionsWithSpecificUser(String userEmail)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
            userEmail, CLIENT_AUTOMATION_ID,
            AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);

    if (limit != -1) {
      if(limit == 0){
        limit = DEFAULT_LIMIT_PARAM_GAME_RESULTS;
      }
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }

    if (status != null) {
      requestSpecification = requestSpecification.queryParam(STATUS_PROP, status);
    }

    if (gameType != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_GAME_TYPES, gameType.getValue());
    }

    if (matchIds != null) {
      requestSpecification = requestSpecification.queryParam(MATCH_IDS_PROP, String.join(",", matchIds));
    }

    return requestSpecification
        .when()
        .get(Endpoints.PredictionApi.PREDICTIONS);
  }
}
