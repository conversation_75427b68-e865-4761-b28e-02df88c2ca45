package com.fansunited.automation.core.apis.voting.entities;

import com.github.javafaker.Faker;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class Tag {
  private String id;
  private String type;
  private String source;

  public static Tag createTagWithRandomData() {
    var faker = new Faker();
    return Tag.builder()
        .id(faker.idNumber().valid())
        .type(faker.file().mimeType())
        .source("football")
        .build();
  }
}
