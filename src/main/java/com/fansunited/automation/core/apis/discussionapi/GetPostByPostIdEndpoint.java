package com.fansunited.automation.core.apis.discussionapi;

import static com.fansunited.automation.constants.UrlParamValues.DiscussionApi.PATH_PARAM_POST_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.helpers.FirebaseHelper.FansUnitedProject.FANS_UNITED_CLIENTS;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetPostByPostIdEndpoint extends BaseSetup {

  public static Response getSinglePost(
          String postId,
          String clientId,
          FirebaseHelper.FansUnitedProject project,
          String email,
          String apiKey,
          ContentType contentType, String authToken)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project, true, email, clientId, apiKey, contentType);

    return requestSpecification
        .pathParams(PATH_PARAM_POST_ID, postId)
        .when()
        .get(Endpoints.DiscussionApi.GET_POST_BY_POST_ID);
  }

  public static Response getSinglePost(String postId) {
    try {
      return getSinglePost(
          postId,
          CLIENT_AUTOMATION_ID,
          FANS_UNITED_CLIENTS,
          null,
          AuthConstants.ENDPOINTS_API_KEY,
          ContentType.JSON, null);
    } catch (HttpException e) {
      throw new RuntimeException(e);
    }
  }
}
