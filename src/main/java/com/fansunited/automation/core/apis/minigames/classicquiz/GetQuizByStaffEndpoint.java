package com.fansunited.automation.core.apis.minigames.classicquiz;

import static com.fansunited.automation.constants.UrlParamValues.MiniGamesApi.QUIZ_ID;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class GetQuizByStaffEndpoint extends QuizBaseSetup {

  public static Response getQuizAsStaff(String quizId, String clientId,
      FirebaseHelper.FansUnitedProject project, String email,
      String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(null, project, true, email, clientId, apiKey,
            contentType);

    return requestSpecification
        .pathParams(QUIZ_ID,quizId )
        .when()
        .get(Endpoints.MiniGamesApi.GET_QUIZ_BY_STAFF);
  }
}
