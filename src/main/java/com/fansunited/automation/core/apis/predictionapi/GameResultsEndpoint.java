package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.PATH_PARAM_GAME_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_START_AFTER;

import com.fansunited.automation.constants.Endpoints;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.http.HttpException;

@Data
@Builder
@NoArgsConstructor @AllArgsConstructor
public class GameResultsEndpoint extends BaseSetup {

  private String gameId;
  private String clientId;
  private String apiKey;
  private ContentType contentType;
  private int limit;
  private String startAfter;


  public Response getGameResults()
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(null, null, null, clientId, apiKey, contentType);

    if (limit != -1) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }

    return requestSpecification
        .pathParam(PATH_PARAM_GAME_ID, gameId)
        .when()
        .get(Endpoints.PredictionApi.GAME_RESULTS);
  }

  public Response getGameCorrectScores()
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(null, null, null, clientId, apiKey, contentType);

    if (limit != -1) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }

    return requestSpecification
        .pathParam(PATH_PARAM_GAME_ID, gameId)
        .when()
        .get(Endpoints.PredictionApi.GAME_CORRECT_RESULTS);
  }
}
