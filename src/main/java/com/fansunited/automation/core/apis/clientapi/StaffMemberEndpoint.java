package com.fansunited.automation.core.apis.clientapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.PLATFORM_OPERATOR;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_CLIENT_ID;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.PATH_PARAM_USER_ID;
import static com.fansunited.automation.constants.UrlParamValues.ClientApi.QUERY_PARAM_ENDPOINT_KEY;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpException;

public class StaffMemberEndpoint extends BaseSetup {

  public static Response createStaffMembers(
      Object body,
      String authToken,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
                1, authToken,
                true,
            project,
            email,
            clientId,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            apiKey,
            contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .body(body)
        .when()
        .post(Endpoints.ClientApi.POST_STAFF_MEMBER);
  }

  public static Response getStaffMembers(
      String authToken,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(
                1, authToken,
                true,
            project,
            email,
            null,
            null,
            AuthConstants.ENDPOINTS_API_KEY,
            apiKey,
            contentType);

    return requestSpecification
        .pathParam(PATH_PARAM_CLIENT_ID, clientId)
        .when()
        .get(Endpoints.ClientApi.GET_STAFF_MEMBER);
  }

  public static Response getStaffMemberByUserAndClientId(
      String authToken,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String userId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
                1, authToken, true, project, email, clientId, userId, null, apiKey, contentType);

    if (clientId != null) {
      requestSpecification = requestSpecification.pathParam(PATH_PARAM_CLIENT_ID, clientId);
    }
    if (userId != null) {
      requestSpecification = requestSpecification.pathParam(PATH_PARAM_USER_ID, userId);
    }
    return requestSpecification
        .when()
        .get(Endpoints.ClientApi.GET_STAFF_MEMBERS_BY_USER_AND_CLIENT_ID);
  }

  public static Response updateStaffMember(
      Object body,
      String authToken,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String userId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
                1, authToken,
                true,
            project,
            email,
            clientId,
            userId,
            AuthConstants.ENDPOINTS_API_KEY,
            apiKey,
            contentType);

    if (userId != null) {
      requestSpecification = requestSpecification.pathParam(PATH_PARAM_USER_ID, userId);
    }
    if (clientId != null) {
      requestSpecification = requestSpecification.pathParam(PATH_PARAM_CLIENT_ID, clientId);
    }
    return requestSpecification
        .body(body)
        .when()
        .patch(Endpoints.ClientApi.UPDATE_STAFF_MEMBER_USER_ID);
  }

  public static Response deleteStaffMember(
      String authToken,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String userId,
      String endpointApiKey,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
            1,
            authToken,
            isAuthRequired,
            project,
            email,
            endpointApiKey,
            clientId,
            userId,
            apiKey,
            contentType);

    if (clientId != null) {
      requestSpecification = requestSpecification.pathParam(PATH_PARAM_CLIENT_ID, clientId);
    }
    if (userId != null) {
      requestSpecification = requestSpecification.pathParam(PATH_PARAM_USER_ID, userId);
    }

    return requestSpecification.when().delete(Endpoints.ClientApi.DELETE_STAFF_MEMBER_USER_ID);
  }

  public static Response setGlobalRoles(
      Object body,
      String authToken,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String clientId,
      String userId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(
                1, authToken, true, project, email, clientId, userId, null, apiKey, contentType);

    if (userId != null) {
      requestSpecification = requestSpecification.pathParam(PATH_PARAM_USER_ID, userId);
    }
    return requestSpecification.body(body).when().put(Endpoints.ClientApi.SET_GLOBAL_ROLES);
  }

  public static Response createStaff(
      FirebaseHelper.FansUnitedProject project, Object body, String clientId) throws HttpException {
    return createStaffMembers(
        body,
        null,
        true,
        project,
        PLATFORM_OPERATOR,
        clientId,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response createStaffNew(
      FirebaseHelper.FansUnitedProject project, Object body, String clientId, String emil)
      throws HttpException {
    return createStaffMembers(
        body,
        null,
        true,
        project,
        emil,
        clientId,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getStaff(FirebaseHelper.FansUnitedProject project, String clientId)
      throws HttpException {
    return getStaffMembers(
        null,
        true,
        project,
        ADMIN_USER,
        clientId,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response getStaff(
      FirebaseHelper.FansUnitedProject project,
      String clientId,
      String apikey,
      ContentType contentType)
      throws HttpException {
    return getStaffMembers(null, true, project, PLATFORM_OPERATOR, clientId, apikey, contentType);
  }

  // GET /v1/staff/{user_id}/clients
  public static Response getStaffByUserId(FirebaseHelper.FansUnitedProject project, String userId,
      String apiKey,
      ContentType contentType)
      throws HttpException {
    return getStaffByUserIds(null, true, project, PLATFORM_OPERATOR, userId, null,
        apiKey, contentType);
  }

  public static Response getStaffByUserIds(
      String authToken,
      boolean isAuthRequired,
      FirebaseHelper.FansUnitedProject project,
      String email,
      String userId,
      String endpointsApiKey,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(1, authToken, isAuthRequired, project, email,
            null, userId, endpointsApiKey, apiKey, contentType);

    if (endpointsApiKey != null) {
      requestSpecification =
          requestSpecification.queryParam(QUERY_PARAM_ENDPOINT_KEY, endpointsApiKey);
    }
    if (userId != null) {
      requestSpecification = requestSpecification.pathParam(PATH_PARAM_USER_ID, userId);
    }

    return requestSpecification
        .pathParam(PATH_PARAM_USER_ID, userId)
        .when()
        .get(Endpoints.ClientApi.GET_STAFF_MEMBER_USER_ID);
  }

  // GET /v1/staff/{user_id}
  public static Response deleteStaffMembers(
      FirebaseHelper.FansUnitedProject project,
      String clientId,
      String userId,
      String apiKey,
      String endpointApiKey,
      ContentType contentType)
      throws HttpException {

    return deleteStaffMember(
        null, true, project, ADMIN_USER, clientId, userId, apiKey, endpointApiKey, contentType);
  }

  public static Response updateStaffMembers(
      FirebaseHelper.FansUnitedProject project,
      Object body,
      String email,
      String clientId,
      String userId,
      String endpointApiKey,
      ContentType contentType)
      throws HttpException {

    return updateStaffMember(
        body, null, project, email, clientId, userId, endpointApiKey, contentType);
  }

  public static Response getStaffMemberByUserAndClientId(
      FirebaseHelper.FansUnitedProject project,
      String clientId,
      String userId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    return getStaffMemberByUserAndClientId(
        null, true, project, PLATFORM_OPERATOR, clientId, userId, apiKey, contentType);
  }

  public static Response setGlobalRolesForStaff(
      FirebaseHelper.FansUnitedProject project,
      Object body,
      String clientId,
      String userId,
      String apiKey,
      ContentType contentType)
      throws HttpException {
    return setGlobalRoles(
        body, null, true, project, PLATFORM_OPERATOR, null, userId, apiKey, contentType);
  }
}
