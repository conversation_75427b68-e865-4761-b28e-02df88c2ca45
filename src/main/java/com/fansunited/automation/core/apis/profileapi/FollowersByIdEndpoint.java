package com.fansunited.automation.core.apis.profileapi;

import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class FollowersByIdEndpoint extends BaseSetup {

  public static Response getFollowersForSpecificUser(String profileId) throws HttpException {

    return getFollowersForSpecificUser(profileId, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON, -1, null);
  }

  public static Response getFollowersForSpecificUserWithDummyQueryParam(String profileId,
      String clientId, String apiKey, ContentType contentType, int limit, String startAfter)
      throws HttpException {

    var requestSpecification = getRequiredRequestSpec(null, false, null,
        null, clientId,
        apiKey, contentType);

    if (limit != -1) {
      requestSpecification =
          requestSpecification.queryParam(UrlParamValues.QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification =
          requestSpecification.queryParam(UrlParamValues.QUERY_PARAM_START_AFTER,
              startAfter);
    }

    return requestSpecification
        .pathParam(UrlParamValues.ProfileApi.PATH_PARAM_PROFILE_ID, profileId)
        .queryParam("dummyParam", System.currentTimeMillis())
        .when()
        .get(Endpoints.ProfileApi.FOLLOWERS_BY_PROFILE_ID);
  }

  public static Response getFollowersForSpecificUser(String userId, String clientId,
      String apiKey,
      ContentType contentType, int limit, String startAfter) throws HttpException {

    var requestSpecification = getRequiredRequestSpec(null, false, null,
        null, clientId,
        apiKey, contentType);

    if (limit != -1) {
      requestSpecification =
          requestSpecification.queryParam(UrlParamValues.QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification =
          requestSpecification.queryParam(UrlParamValues.QUERY_PARAM_START_AFTER,
              startAfter);
    }

    return requestSpecification
        .pathParam(UrlParamValues.ProfileApi.PATH_PARAM_USER_ID, userId)
        .when()
        .get(Endpoints.ProfileApi.FOLLOWERS_BY_PROFILE_ID);
  }
}
