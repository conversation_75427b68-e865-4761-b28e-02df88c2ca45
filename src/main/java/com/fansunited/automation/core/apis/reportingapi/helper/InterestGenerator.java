package com.fansunited.automation.core.apis.reportingapi.helper;

import static com.fansunited.automation.core.apis.footballapi.CompetitionsEndpoint.getRandomCompetitionId;
import static com.fansunited.automation.core.apis.footballapi.TeamsEndpoint.getRandomTeamId;
import static com.fansunited.automation.core.apis.footballapi.TopPlayersEndpoint.getRandomTopPlayerId;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;

import com.fansunited.automation.constants.ApiConstants;
import com.fansunited.automation.model.profileapi.profile.ProfileData;
import java.util.UUID;

public class InterestGenerator {

  private InterestGenerator() {
  }

  public static ProfileData.Interest generateFootballInterestForCompType(String compId,
      boolean favorite) {

    return new ProfileData.Interest(favorite, compId,
        ApiConstants.ProfileApi.Interest.FOOTBALL.getSource(),
        ApiConstants.ProfileApi.Interest.Football.COMPETITION.getType());
  }

  public static ProfileData.Interest generateFootballInterest(
      ApiConstants.ProfileApi.Interest.Football type, boolean favorite, String id) {
    ProfileData.Interest interest = null;

    switch (type) {
      case PLAYER -> interest = new ProfileData.Interest(favorite, id,
          ApiConstants.ProfileApi.Interest.FOOTBALL.getSource(),
          ApiConstants.ProfileApi.Interest.Football.PLAYER.getType());
      case TEAM -> interest = new ProfileData.Interest(favorite, id,
          ApiConstants.ProfileApi.Interest.FOOTBALL.getSource(),
          ApiConstants.ProfileApi.Interest.Football.TEAM.getType());
      case COMPETITION -> interest = new ProfileData.Interest(favorite, id,
          ApiConstants.ProfileApi.Interest.FOOTBALL.getSource(),
          ApiConstants.ProfileApi.Interest.Football.COMPETITION.getType());
    }
    return interest;
  }

  public static ProfileData.Interest generateFootballInterest(
      ApiConstants.ProfileApi.Interest.Football type, boolean favorite) {

    switch (type) {
      case PLAYER -> {
        String footballPlayerId = getRandomTopPlayerId();
        return generateFootballInterest(type, favorite, footballPlayerId);
      }
      case TEAM -> {
        String footballTeamId = getRandomTeamId();
        return generateFootballInterest(type, favorite, footballTeamId);
      }
      case COMPETITION -> {
        String competitionId = getRandomCompetitionId();
        return generateFootballInterest(type, favorite, competitionId);
      }
      default -> throw new IllegalArgumentException(
          "Logic for " + type.getType() + " not implemented!");
    }
  }

  public static ProfileData.Interest generateTennisInterest(
      ApiConstants.ProfileApi.Interest.Tennis type, boolean favorite) {
    ProfileData.Interest interest = null;

    switch (type) {
      case PLAYER -> {
        String tennisPlayerId = "fb:t:1342";
        interest = new ProfileData.Interest(favorite, tennisPlayerId,
            ApiConstants.ProfileApi.Interest.TENNIS.getSource(),
            ApiConstants.ProfileApi.Interest.Tennis.PLAYER.getTennisIntType());
      }
      case COMPETITION -> {
        String competitionId = "fb:c:1525";
        interest = new ProfileData.Interest(favorite, competitionId,
            ApiConstants.ProfileApi.Interest.TENNIS.getSource(),
            ApiConstants.ProfileApi.Interest.Tennis.COMPETITION.getTennisIntType());
      }
    }
    return interest;
  }

  public static ProfileData.Interest generateRandomFootballInterest(
      ApiConstants.ProfileApi.Interest.Football type, boolean favorite) {
        String footballTeamId = "fb:t:" + generateRandomNumber(1, 9999999);
        return generateFootballInterest(type, favorite, footballTeamId);
  }

  public static ProfileData.Interest generateSportInterest(
      ApiConstants.ProfileApi.Interest.Sport type,
      boolean favorite) {
    return switch (type) {
      case FOOTBALL -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.FOOTBALL.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case CRICKET -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.CRICKET.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case FIELD_HOCKEY -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.FIELD_HOCKEY.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case TENNIS -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.TENNIS.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case VOLLEYBALL -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.VOLLEYBALL.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case TABLE_TENNIS -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.TABLE_TENNIS.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case BASKETBALL -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.BASKETBALL.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case BASEBALL -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.BASEBALL.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case RUGBY -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.RUGBY.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case GOLF -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.GOLF.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case ATHLETICS -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.ATHLETICS.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case FORMULA_1 -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.FORMULA_1.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case BOXING -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.BOXING.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case ICE_HOCKEY -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.ICE_HOCKEY.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case AMERICAN_FOOTBALL -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.AMERICAN_FOOTBALL.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case MMA -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.MMA.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case MOTO_GP -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.MOTO_GP.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case BADMINTON -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.BADMINTON.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case CYCLING -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.CYCLING.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case SNOOKER -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.SNOOKER.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case GYMNASTICS -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.GYMNASTICS.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case HANDBALL -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.HANDBALL.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case WRESTLING -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.WRESTLING.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case HORSE_RACING -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.HORSE_RACING.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case WEIGHT_LIFTING -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.WEIGHT_LIFTING.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case CHESS -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.CHESS.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case SQUASH -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.SQUASH.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case BIATHLON -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.BIATHLON.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case WINTER_SPORTS -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.WINTER_SPORTS.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case MARTIAL_ARTS -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.MARTIAL_ARTS.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case WATER_SPORTS -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.WATER_SPORTS.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case SHOOTING -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.SHOOTING.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case RALLY -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.RALLY.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
      case RHYTHMIC_GYMNASTICS -> new ProfileData.Interest(favorite,
          ApiConstants.ProfileApi.Interest.Sport.RHYTHMIC_GYMNASTICS.getSportIntType(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource(),
          ApiConstants.ProfileApi.Interest.SPORT.getSource());
    };
  }

  public static ProfileData.Interest generateCustomInterest(boolean favorite, String type) {
    return new ProfileData.Interest(favorite, UUID.randomUUID().toString(),
        ApiConstants.ProfileApi.Interest.CUSTOM.getSource(),
        type);
  }
}
