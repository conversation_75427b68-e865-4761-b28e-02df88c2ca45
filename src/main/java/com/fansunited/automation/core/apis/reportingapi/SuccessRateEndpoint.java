package com.fansunited.automation.core.apis.reportingapi;

import static com.fansunited.automation.constants.AuthConstants.FansUnitedClientsProject.ADMIN_USER;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_FROM_DATE;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_TO_DATE;

import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.helpers.FirebaseHelper;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import org.apache.http.HttpException;

public class SuccessRateEndpoint extends BaseSetup {

  public static Response getSuccessRateForClient(String clientId, String fromDate, String toDate,
      FirebaseHelper.FansUnitedProject project,
      String apiKey, ContentType contentType) throws HttpException {

    return getSuccessRatePerClient(clientId, fromDate, toDate, null, ADMIN_USER,
        project, apiKey,
        contentType);
  }
  public static Response getSuccessRateForSportsEntities(String clientId, String fromDate, String toDate,
      FirebaseHelper.FansUnitedProject project,
      String apiKey, ContentType contentType) throws HttpException {

    return getSuccessRatePerSportsEntities(clientId, fromDate, toDate, null, ADMIN_USER,
        project, apiKey,
        contentType);
  }

  public static Response getSuccessRatePerClient(String clientId, String fromDate, String toDate
      , String authToken, String email,
      FirebaseHelper.FansUnitedProject project, String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project,
            email, clientId, apiKey, contentType);

    if (fromDate != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_FROM_DATE, fromDate);
    }

    if (toDate != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_TO_DATE, toDate);
    }
    return requestSpecification
        .when()
        .get(Endpoints.ReportingApi.SUCCESS_RATE_FOR_CLIENT);
  }
  public static Response getSuccessRatePerSportsEntities(String clientId, String fromDate, String toDate
      , String authToken, String email,
      FirebaseHelper.FansUnitedProject project, String apiKey, ContentType contentType)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, project,
            email, clientId, apiKey, contentType);

    if (fromDate != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_FROM_DATE, fromDate);
    }

    if (toDate != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_TO_DATE, toDate);
    }
    return requestSpecification
        .when()
        .get(Endpoints.ReportingApi.SUCCESS_RATE_SPORTS_ENTITIES);
  }

}
