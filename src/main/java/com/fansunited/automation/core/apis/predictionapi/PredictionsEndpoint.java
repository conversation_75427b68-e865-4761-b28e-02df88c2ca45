package com.fansunited.automation.core.apis.predictionapi;

import static com.fansunited.automation.constants.ApiConstants.FootballApi.PLAYER_ID_FOR_LOST_PREDICTIONS;
import static com.fansunited.automation.constants.ApiConstants.FootballApi.VALID_PLAYER_ID;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.ID_PROP;
import static com.fansunited.automation.constants.ApiConstants.PredictionApi.STATUS_PROP;
import static com.fansunited.automation.constants.UrlParamValues.PredictionApi.QUERY_PARAM_GAME_TYPES;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_LIMIT;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_START_AFTER;
import static com.fansunited.automation.core.apis.clientapi.FeaturesEndpoint.getFullCoverageCompetitionsWhitelist;
import static com.fansunited.automation.core.apis.footballapi.MatchByIdEndpoint.getRandomPlayerFromMatch;
import static com.fansunited.automation.core.apis.footballapi.MatchesEndpoint.getMatchesIdListAfterDate;
import static com.fansunited.automation.core.base.AuthBase.createUser;
import static com.fansunited.automation.core.base.AuthBase.getCurrentTestUser;
import static com.fansunited.automation.helpers.Helper.generateRandomNumber;
import static com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.DoubleChanceOutcome.getRandomDoubleChanceOutcome;
import static com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.HalfTimeFullTimeOutcome.ONE_ONE;
import static com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.HalfTimeFullTimeOutcome.TWO_DRAW;
import static com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.HalfTimeFullTimeOutcome.getRandomHalfTimeFullTimeOutcome;
import static com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.OneXTwoOutcome.ONE;
import static com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.OneXTwoOutcome.TWO;
import static com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.OneXTwoOutcome.getRandomOneXTwoOutcome;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.core.resolver.MatchGenerator;
import com.fansunited.automation.core.resolver.Resolver;
import com.fansunited.automation.core.resolver.hibernate.Match;
import com.fansunited.automation.helpers.FirebaseHelper;
import com.fansunited.automation.helpers.Helper;
import com.fansunited.automation.model.predictionapi.games.GameFixture;
import com.fansunited.automation.model.predictionapi.games.enums.GameType;
import com.fansunited.automation.model.predictionapi.games.enums.MatchType;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fansunited.automation.model.predictionapi.games.enums.PredictionStatus;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.BothTeamsScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CornersMatchPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CorrectScoreAdvancedPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CorrectScoreHTPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.CorrectScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.DoubleChancePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.FullTimeOneXTwoPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.HalfTimeFullTimePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.HalfTimeOneXTwoPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverCorners_10_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverCorners_11_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverCorners_12_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverCorners_13_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverCorners_6_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverCorners_7_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverCorners_8_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverCorners_9_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverGoals_0_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverGoals_1_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverGoals_2_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverGoals_3_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverGoals_4_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverGoals_5_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.OverGoals_6_5_PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PenaltyMatchPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerRedCardPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerScoreFirstGoalPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerScoreHattrickPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerScorePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerScoreTwicePredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PlayerYellowCardPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.PredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.RedCardMatchPredictionFixture;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.DoubleChanceOutcome;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.OverCornersValue;
import com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums.OverGoalsValue;
import com.fansunited.automation.model.predictionapi.games.request.CreatePredictionRequest;
import com.fansunited.automation.model.predictionapi.games.response.GameInstance;
import com.fansunited.automation.model.predictionapi.predictions.AdvancedPredictions;
import com.google.firebase.auth.FirebaseAuthException;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import org.apache.http.HttpException;
import org.junit.jupiter.api.Assertions;

public class PredictionsEndpoint extends BaseSetup {

  // GET /v1/predictions
  public static Response getOwnPredictionsForGameTypes(String gameTypes, int limit,
      String startAfter) throws HttpException {

    return getOwnPredictions(gameTypes, null, null,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, getCurrentTestUser().getEmail(),
        CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, limit, startAfter);
  }

  public static Response getOwnPredictions() throws HttpException {
    return getOwnPredictions(null);
  }

  public static Response getOwnPredictions(String status) throws HttpException {
    return getOwnPredictions(status, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON, -1, null);
  }

  public static Response getOwnPredictions(String status, String clientId, String apiKey,
      ContentType contentType, int limit, String startAfter) throws HttpException {

    return getOwnPredictions(null, status, null,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        getCurrentTestUser().getEmail(), clientId, apiKey, contentType, limit, startAfter);
  }

  public static Response getOwnPredictions(String status, String authToken, String clientId,
      String apiKey,
      ContentType contentType) throws HttpException {

    return getOwnPredictions(null, status, authToken, null, null, clientId, apiKey, contentType, -1,
        null);
  }

  public static Response getOwnPredictionsForUser(String email) throws HttpException {

    return getOwnPredictions(null, null, null,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        email, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON, -1, null);
  }

  public static Response getOwnPredictions(String gameTypes, String status, String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject, String email, String clientId,
      String apiKey,
      ContentType contentType, int limit, String startAfter)
      throws HttpException {

    var requestSpecification =
        getRequiredRequestSpec(authToken, tokenForProject,
            email, clientId,
            apiKey,

            contentType);

    if (limit != -1) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_LIMIT, limit);
    }

    if (startAfter != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_START_AFTER, startAfter);
    }

    if (status != null) {
      requestSpecification = requestSpecification.queryParam(STATUS_PROP, status);
    }

    if (gameTypes != null) {
      requestSpecification = requestSpecification.queryParam(QUERY_PARAM_GAME_TYPES, gameTypes);
    }

    return requestSpecification
        .when()
        .get(Endpoints.PredictionApi.PREDICTIONS);
  }


  // OPTIONS prediction API
  public static Response optionsPredictionApi(String endpoint) throws HttpException {
    var requestSpecification = getRequiredRequestSpec(null, null, null, null, AuthConstants.ENDPOINTS_API_KEY, null);

    return requestSpecification.when().options(endpoint);
  }

  // POST /v1/predictions
  public static String createSinglePrediction(String matchId, String playerId,
      PredictionMarket market)
      throws HttpException {

    var response = createPrediction(CreatePredictionRequest.builder()
        .fixtures(generateValidSinglePredictionFixture(market, matchId,
            playerId))
        .build());

    return response.then().extract().body().jsonPath().get(ID_PROP);
  }

  public static List<String> createSinglePredictions(int numberOfPredictions)
      throws HttpException {

    var matchesIdList = getMatchesIdListAfterDate(getFullCoverageCompetitionsWhitelist(),
        Helper.generateDateTimeInIsoFormat(Helper.generateFutureDate(12)), numberOfPredictions);

    var predictionMarkets = PredictionMarket.getValidMarkets();

    var predictionsIdList = new ArrayList<String>();

    for (String matchId : matchesIdList) {
      var response = createPrediction(CreatePredictionRequest.builder()
          .fixtures(generateValidSinglePredictionFixture(
              predictionMarkets.get(generateRandomNumber(1, predictionMarkets.size() - 1)), matchId,
              getRandomPlayerFromMatch(matchId)))
          .build());

      predictionsIdList.add(response.then().extract().body().jsonPath().get(ID_PROP));
    }
    return predictionsIdList;
  }

  public static List<String> createPredictionsForGame(String gameId, GameType gameType,
      int numberOfPredictions)
      throws HttpException, IOException, FirebaseAuthException, InterruptedException,
      ExecutionException {

    var predictionsIdList = new ArrayList<String>();

    for (int i = 0; i < numberOfPredictions; i++) {
      var user = createUser();
      predictionsIdList.add(createPredictionForGame(user.getEmail(), gameId, gameType));
    }
    return predictionsIdList;
  }

  public static List<String> createGamePredictions(GameType gameType, int numberOfPredictions)
      throws HttpException, IllegalArgumentException {

    List<String> gamesIdList = new ArrayList<>();

    for (int i = 0; i < numberOfPredictions; i++) {
      List<Match> matchList = MatchGenerator.generateMatches(
          gameType == GameType.TOP_X ? 6 : 1, false);

      Resolver.openMatchesForPredictions(matchList);

      LocalDateTime localDateTime =
          matchList.stream().map(Match::getKickoffAt).sorted().toList().get(0);

      var gameInstance = CreateGameEndpoint.builder()
          .gameType(gameType)
          .predictionsCutoff(localDateTime.atZone(ZoneId.of("UTC")).minusMinutes(16))
          .matchesIdList(matchList.stream().map(Match::getId).collect(Collectors.toList()))
          .build()
          .createGame()
          .as(GameInstance.class);

      gamesIdList.add(gameInstance.getId());
    }

    var predictionsIdList = new ArrayList<String>();

    switch (gameType) {
      case TOP_X -> {
        for (String gameId : gamesIdList) {
          predictionsIdList.add(
              createPredictionForGame(getCurrentTestUser().getEmail(), gameId, GameType.TOP_X));
        }
      }
      case MATCH_QUIZ -> {
        for (String gameId : gamesIdList) {
          predictionsIdList.add(createPredictionForGame(getCurrentTestUser().getEmail(), gameId,
              GameType.MATCH_QUIZ));
        }
      }
    }

    return predictionsIdList;
  }

  public static List<String> createPredictionForGames(String userEmail, List<String> gameIds,
      GameType gameType) throws HttpException {

    var predictionsIdList = new ArrayList<String>();

    for (String gameId : gameIds) {
      predictionsIdList.add(createPredictionForGame(userEmail, gameId, gameType));
    }

    return predictionsIdList;
  }

  public static String createPredictionForGame(String userEmail, String gameId,
      GameType gameType)
      throws HttpException, IllegalArgumentException {

    switch (gameType) {
      case TOP_X -> {
        var createGameResponse = GameEndpoint.getGameById(gameId);

        var gameObject = createGameResponse.as(GameInstance.class);

        var matchesIdList =
            gameObject.getFixtures().stream().map(GameFixture::getMatchId).toList();

        var predictions = new ArrayList<PredictionFixture>();

        matchesIdList.forEach(matchId -> predictions.addAll(
            generateValidSinglePredictionFixture(PredictionMarket.CORRECT_SCORE, matchId,
                null)));

        var response = createPredictionForUser(CreatePredictionRequest.builder()
            .fixtures(predictions)
            .gameInstanceId(gameObject.getId())
            .build(), userEmail);

        var predictionId = response.then().extract().body().jsonPath().getString(ID_PROP);

        Assertions.assertNotNull(predictionId, "Prediction ID is NULL!");

        return predictionId;
      }
      case MATCH_QUIZ -> {

        var createGameResponse = GameEndpoint.getGameById(gameId);

        var gameObject = createGameResponse.as(GameInstance.class);

        var matchId = gameObject.getFixtures().get(0).getMatchId();

        var playerId = getRandomPlayerFromMatch(matchId);

        var predictions = getAllFixtures(matchId, playerId);

        var response = createPredictionForUser(CreatePredictionRequest.builder()
            .fixtures(predictions)
            .gameInstanceId(gameObject.getId())
            .build(), userEmail);

        var predictionId = response.then().extract().body().jsonPath().getString(ID_PROP);

        Assertions.assertNotNull(predictionId, "Prediction ID is NULL!");

        return predictionId;
      }
      default -> throw new IllegalArgumentException(
          "Game type is not in the list of valid game types: " + GameType.getValidGameTypes());
    }
  }

  public static List<PredictionFixture> generateValidSinglePredictionFixture(
      PredictionMarket market, String matchId, String playerId) {
    var goalHome = generateRandomNumber(0, 5);
    var goalAway = generateRandomNumber(0, 5);

    switch (market) {
      case CORRECT_SCORE -> {
        return List.of(CorrectScorePredictionFixture.builder()
            .matchId(matchId)
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsAway(goalHome)
            .goalsHome(goalAway)
            .build());
      }
      case CORRECT_SCORE_HT -> {
        return List.of(CorrectScoreHTPredictionFixture.builder()
            .matchId(matchId)
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsAway(generateRandomNumber(0, goalHome))
            .goalsHome(generateRandomNumber(0, goalAway))
            .build());
      }
      case CORRECT_SCORE_ADVANCED -> {
        return List.of(CorrectScoreAdvancedPredictionFixture.builder()
            .matchId(matchId)
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsAway(goalHome)
            .goalsHome(goalAway)
            .build());
      }
      case BOTH_TEAMS_SCORE -> {
        return List.of(BothTeamsScorePredictionFixture.builder()
            .matchId(matchId)
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .build());
      }
      case DOUBLE_CHANCE -> {
        return List.of(DoubleChancePredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(getRandomDoubleChanceOutcome())
            .build());
      }
      case FT_1X2 -> {
        return List.of(FullTimeOneXTwoPredictionFixture.builder()
            .matchId(matchId)
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(getRandomOneXTwoOutcome())
            .build());
      }
      case HT_1X2 -> {
        return List.of(HalfTimeOneXTwoPredictionFixture.builder()
            .matchId(matchId)
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(getRandomOneXTwoOutcome())
            .build());
      }
      case HT_FT -> {
        return List.of(HalfTimeFullTimePredictionFixture.builder()
            .matchId(matchId)
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(getRandomHalfTimeFullTimeOutcome())
            .build());
      }
      case PLAYER_SCORE -> {
        return List.of(PlayerScorePredictionFixture.builder()
            .matchId(matchId)
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .playerId(playerId)
            .build());
      }
      case PLAYER_RED_CARD -> {
        return List.of(PlayerRedCardPredictionFixture.builder()
            .matchId(matchId)
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(false)
            .playerId(playerId)
            .build());
      }
      case PLAYER_SCORE_FIRST_GOAL -> {
        return List.of(PlayerScoreFirstGoalPredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .playerId(playerId)
            .build());
      }
      case PLAYER_SCORE_HATTRICK -> {
        return List.of(PlayerScoreHattrickPredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .playerId(playerId)
            .build());
      }
      case PLAYER_SCORE_TWICE -> {
        return List.of(PlayerScoreTwicePredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .playerId(playerId)
            .build());
      }
      case PLAYER_YELLOW_CARD -> {
        return List.of(PlayerYellowCardPredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .playerId(playerId)
            .build());
      }
      case PENALTY_MATCH -> {
        return List.of(PenaltyMatchPredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case RED_CARD_MATCH -> {
        return List.of(RedCardMatchPredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_GOALS_2_5 -> {
        return List.of(OverGoals_2_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_GOALS_0_5 -> {
        return List.of(OverGoals_0_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_GOALS_1_5 -> {
        return List.of(OverGoals_1_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_GOALS_3_5 -> {
        return List.of(OverGoals_3_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_GOALS_4_5 -> {
        return List.of(OverGoals_4_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_GOALS_5_5 -> {
        return List.of(OverGoals_5_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_GOALS_6_5 -> {
        return List.of(OverGoals_6_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_CORNERS_6_5 -> {
        return List.of(OverCorners_6_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_CORNERS_7_5 -> {
        return List.of(OverCorners_7_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }

      case OVER_CORNERS_8_5 -> {
        return List.of(OverCorners_8_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_CORNERS_9_5 -> {
        return List.of(OverCorners_9_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_CORNERS_10_5 -> {
        return List.of(OverCorners_10_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_CORNERS_11_5 -> {
        return List.of(OverCorners_11_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_CORNERS_12_5 -> {
        return List.of(OverCorners_12_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case OVER_CORNERS_13_5 -> {
        return List.of(OverCorners_13_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
      }
      case CORNERS_MATCH -> {
        return List.of(CornersMatchPredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(6)
            .build());
      }
      default -> throw new IllegalArgumentException(
          "Unsupported market provided! Valid markets: " + PredictionMarket.getValidMarkets());
    }
  }

  public static List<PredictionFixture> getAllFixtures(String matchId, String playerId) {
    var predictionFixturesList = new ArrayList<PredictionFixture>();
    for (PredictionMarket market : PredictionMarket.getValidMarkets()) {
      predictionFixturesList.addAll(
          generateValidSinglePredictionFixture(market, matchId, playerId));
    }
    return predictionFixturesList;
  }

  public static List<PredictionFixture> getPlayerFixtures(String matchId, String playerId) {
    var predictionFixturesList = new ArrayList<PredictionFixture>();
    for (PredictionMarket market : PredictionMarket.getPlayerMarkets()) {
      predictionFixturesList.addAll(
          generateValidSinglePredictionFixture(market, matchId, playerId));
    }
    return predictionFixturesList;
  }

  public static Response createPrediction(
      Object body)
      throws HttpException {

    return createPrediction(body, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY,
        ContentType.JSON);
  }

  public static Response createPrediction(Object body, String clientId, String apiKey,
      ContentType contentType) throws HttpException {

    return createPrediction(body, null, FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
           getCurrentTestUser().getEmail(), clientId, apiKey, contentType);
  }

  public static Response createPredictionForUser(Object body, String email)
      throws HttpException {

    return createPrediction(body, null, FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE,
        email, CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static List<String> createPredictionsForGameWithSpecificUser(String gameId, GameType gameType, String email,
      int numberOfPredictions)
      throws HttpException {

    var predictionsIdList = new ArrayList<String>();

    for (int i = 0; i < numberOfPredictions; i++) {
      predictionsIdList.add(createPredictionForGame(email, gameId, gameType));
    }
    return predictionsIdList;
  }

  public static Response createPrediction(Object body, String authToken) throws HttpException {

    return createPrediction(body, authToken, null, null, CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }
  public static Response createPredictionWithHeader(Object body, String deviceId ) throws HttpException {

    return createPredictionWithHeaders(body, null, FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, getCurrentTestUser().getEmail(), CLIENT_AUTOMATION_ID,
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, deviceId);
  }
  public static Response createPrediction(
      Object body,
      String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject, String email, String clientId,
      String apiKey,
      ContentType contentType)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpec(authToken, tokenForProject,
            email, clientId,
            apiKey,
            contentType);

    return requestSpecification
        .body(body)
        .when()
        .post(Endpoints.PredictionApi.PREDICTIONS);
  }

  public static Response createPredictionWithHeaders(
      Object body,
      String authToken,
      FirebaseHelper.FansUnitedProject tokenForProject, String email, String clientId,
      String apiKey,
      ContentType contentType,String deviceId)
      throws HttpException {

    RequestSpecification requestSpecification =
        getRequiredRequestSpecWithHeaders(authToken, tokenForProject,
            email, clientId,
            apiKey,
            contentType, deviceId);

    return requestSpecification
        .body(body)
        .when()
        .post(Endpoints.PredictionApi.PREDICTIONS);
  }

  /**
   * This method returns a built prediction based on the type of OverGoals
   *
   * @param overGoalsValue - the type of OverGoals
   * @param matchId        - the id of the match used for the prediction
   * @return the built prediction based on the type of OverGoals
   */
  public static List<? extends PredictionFixture> getPredictionFixturesForOverGoalMarkets(
      OverGoalsValue overGoalsValue, String matchId) {
    List<?> prediction;

    switch (overGoalsValue) {
      case OVER_0_5:
        prediction = List.of(OverGoals_0_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;
      case OVER_1_5:
        prediction = List.of(OverGoals_1_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      case OVER_2_5:
        prediction = List.of(OverGoals_2_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      case OVER_3_5:
        prediction = List.of(OverGoals_3_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      case OVER_4_5:
        prediction = List.of(OverGoals_4_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      case OVER_5_5:
        prediction = List.of(OverGoals_5_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      case OVER_6_5:
        prediction = List.of(OverGoals_6_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      default:
        throw new IllegalArgumentException(overGoalsValue + " is not legal value for OverGoals");
    }
    return (List<? extends PredictionFixture>) prediction;
  }

  /**
   * This method returns a built prediction based on the type of OverCorners
   *
   * @param overCornersValue - the type of OverCorners
   * @param matchId          - the id of the match used for the prediction
   * @return the built prediction based on the type of OverCorners
   */
  public static List<? extends PredictionFixture> getPredictionFixturesForOverCorners(
      OverCornersValue overCornersValue, String matchId) {
    List<?> prediction;

    switch (overCornersValue) {
      case OVER_6_5:
        prediction = List.of(OverCorners_6_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;
      case OVER_7_5:
        prediction = List.of(OverCorners_7_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      case OVER_8_5:
        prediction = List.of(OverCorners_8_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      case OVER_9_5:
        prediction = List.of(OverCorners_9_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      case OVER_10_5:
        prediction = List.of(OverCorners_10_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      case OVER_11_5:
        prediction = List.of(OverCorners_11_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      case OVER_12_5:
        prediction = List.of(OverCorners_12_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;

      case OVER_13_5:
        prediction = List.of(OverCorners_13_5_PredictionFixture.builder()
            .matchType(MatchType.FOOTBALL.getValue())
            .matchId(matchId)
            .prediction(true)
            .build());
        break;
      default:
        throw new IllegalArgumentException(
            overCornersValue + " is not legal value for OverCorners");
    }
    return (List<? extends PredictionFixture>) prediction;
  }

  /**
   * The method sets for each valid market correct prediction as per the preliminary set match statistics
   *
   * @param markets            - all valid market
   * @param match              - the match for which predictions will be set
   * @param predictionFixtures - all valid markets fixtures
   */
  public static void setValidPredictionsForAllMarkets(List<PredictionMarket> markets, Match match,
      ArrayList<PredictionFixture> predictionFixtures) {
    markets.forEach(market -> {
      switch (market) {
        case CORNERS_MATCH -> predictionFixtures.add(CornersMatchPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners())
            .build());

        case CORRECT_SCORE -> predictionFixtures.add(CorrectScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsHome(match.getGoalsFullTimeHome())
            .goalsAway(match.getGoalsFullTimeAway())
            .build());

        case CORRECT_SCORE_HT -> predictionFixtures.add(CorrectScoreHTPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsHome(match.getGoalsHalfTimeHome())
            .goalsAway(match.getGoalsHalfTimeAway())
            .build());

        case CORRECT_SCORE_ADVANCED ->
            predictionFixtures.add(CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(match.getGoalsFullTimeHome())
                .goalsAway(match.getGoalsFullTimeAway())
                .build());

        case FT_1X2 -> predictionFixtures.add(FullTimeOneXTwoPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(ONE)
            .build());

        case BOTH_TEAMS_SCORE -> predictionFixtures.add(BothTeamsScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .build());
        case HT_FT -> predictionFixtures.add(HalfTimeFullTimePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(ONE_ONE)
            .build());
        case OVER_GOALS_0_5 -> predictionFixtures.add(OverGoals_0_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 0.5f)
            .build());
        case OVER_GOALS_1_5 -> predictionFixtures.add(OverGoals_1_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 1.5f)
            .build());
        case OVER_GOALS_2_5 -> predictionFixtures.add(OverGoals_2_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 2.5f)
            .build());
        case OVER_GOALS_3_5 -> predictionFixtures.add(OverGoals_3_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 3.5f)
            .build());
        case OVER_GOALS_4_5 -> predictionFixtures.add(OverGoals_4_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 4.5f)
            .build());
        case OVER_GOALS_5_5 -> predictionFixtures.add(OverGoals_5_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 5.5f)
            .build());
        case OVER_GOALS_6_5 -> predictionFixtures.add(OverGoals_6_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 6.5f)
            .build());
        case HT_1X2 -> predictionFixtures.add(HalfTimeOneXTwoPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(ONE)
            .build());
        case OVER_CORNERS_6_5 -> predictionFixtures.add(OverCorners_6_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() > 6.5f)
            .build());
        case OVER_CORNERS_7_5 -> predictionFixtures.add(OverCorners_7_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() > 7.5f)
            .build());
        case OVER_CORNERS_8_5 -> predictionFixtures.add(OverCorners_8_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() > 8.5f)
            .build());
        case OVER_CORNERS_9_5 -> predictionFixtures.add(OverCorners_9_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() > 9.5f)
            .build());
        case OVER_CORNERS_10_5 ->
            predictionFixtures.add(OverCorners_10_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() > 10.5f)
                .build());
        case OVER_CORNERS_11_5 ->
            predictionFixtures.add(OverCorners_11_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() > 11.5f)
                .build());
        case OVER_CORNERS_12_5 ->
            predictionFixtures.add(OverCorners_12_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() > 12.5f)
                .build());
        case OVER_CORNERS_13_5 ->
            predictionFixtures.add(OverCorners_13_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() > 13.5f)
                .build());
        case PLAYER_SCORE -> predictionFixtures.add(PlayerScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .playerId(VALID_PLAYER_ID)
            .build());
        case PENALTY_MATCH -> predictionFixtures.add(PenaltyMatchPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(false)
            .build());
        case DOUBLE_CHANCE -> predictionFixtures.add(DoubleChancePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(DoubleChanceOutcome.ONE_DRAW)
            .build());
        case RED_CARD_MATCH -> predictionFixtures.add(RedCardMatchPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .build());
        case PLAYER_RED_CARD -> predictionFixtures.add(PlayerRedCardPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .playerId(VALID_PLAYER_ID)
            .build());
        case PLAYER_SCORE_TWICE -> predictionFixtures.add(
            PlayerScoreTwicePredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .playerId(VALID_PLAYER_ID)
                .build());
        case PLAYER_SCORE_HATTRICK -> predictionFixtures.add(
            PlayerScoreHattrickPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .playerId(VALID_PLAYER_ID)
                .build());
        case PLAYER_YELLOW_CARD -> predictionFixtures.add(
            PlayerYellowCardPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .playerId(VALID_PLAYER_ID)
                .build());
        case PLAYER_SCORE_FIRST_GOAL -> predictionFixtures.add(
            PlayerScoreFirstGoalPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .playerId(VALID_PLAYER_ID)
                .build());
      }
    });
  }

  /**
   * The method sets for each valid market incorrect prediction as per the preliminary set match statistics
   *
   * @param markets            - all valid market
   * @param match              - the match for which predictions will be set
   * @param predictionFixtures - all valid markets fixtures
   */
  public static void setInvalidPredictionsForAllMarkets(List<PredictionMarket> markets, Match match,
      ArrayList<PredictionFixture> predictionFixtures) {
    markets.forEach(market -> {
      switch (market) {
        case CORNERS_MATCH -> predictionFixtures.add(CornersMatchPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners())
            .build());
        case CORRECT_SCORE -> predictionFixtures.add(CorrectScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsHome(match.getGoalsFullTimeHome() + 1)
            .goalsAway(match.getGoalsFullTimeAway())
            .build());
        case CORRECT_SCORE_HT -> predictionFixtures.add(CorrectScoreHTPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsHome(match.getGoalsHalfTimeHome())
            .goalsAway(match.getGoalsHalfTimeAway() + 1)
            .build());
        case CORRECT_SCORE_ADVANCED ->
            predictionFixtures.add(CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(match.getGoalsFullTimeHome() - 2)
                .goalsAway(match.getGoalsFullTimeAway() + 2)
                .build());
        case FT_1X2 -> predictionFixtures.add(FullTimeOneXTwoPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(TWO)
            .build());
        case BOTH_TEAMS_SCORE -> predictionFixtures.add(BothTeamsScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(false)
            .build());
        case HT_FT -> predictionFixtures.add(HalfTimeFullTimePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(TWO_DRAW)
            .build());
        case OVER_GOALS_0_5 -> predictionFixtures.add(OverGoals_0_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() < 0.5f)
            .build());
        case OVER_GOALS_1_5 -> predictionFixtures.add(OverGoals_1_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() < 1.5f)
            .build());
        case OVER_GOALS_2_5 -> predictionFixtures.add(OverGoals_2_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() < 2.5f)
            .build());
        case OVER_GOALS_3_5 -> predictionFixtures.add(OverGoals_3_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() < 3.5f)
            .build());
        case OVER_GOALS_4_5 -> predictionFixtures.add(OverGoals_4_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() < 4.5f)
            .build());
        case OVER_GOALS_5_5 -> predictionFixtures.add(OverGoals_5_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() < 5.5f)
            .build());
        case OVER_GOALS_6_5 -> predictionFixtures.add(OverGoals_6_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() < 6.5f)
            .build());
        case OVER_CORNERS_6_5 -> predictionFixtures.add(OverCorners_6_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() < 6.5f)
            .build());
        case OVER_CORNERS_7_5 -> predictionFixtures.add(OverCorners_7_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() < 7.5f)
            .build());
        case OVER_CORNERS_8_5 -> predictionFixtures.add(OverCorners_8_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() < 8.5f)
            .build());
        case OVER_CORNERS_9_5 -> predictionFixtures.add(OverCorners_9_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() < 9.5f)
            .build());
        case OVER_CORNERS_10_5 ->
            predictionFixtures.add(OverCorners_10_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() < 10.5f)
                .build());
        case OVER_CORNERS_11_5 ->
            predictionFixtures.add(OverCorners_11_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() < 11.5f)
                .build());
        case OVER_CORNERS_12_5 ->
            predictionFixtures.add(OverCorners_12_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() < 12.5f)
                .build());
        case OVER_CORNERS_13_5 ->
            predictionFixtures.add(OverCorners_13_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() < 13.5f)
                .build());
        case HT_1X2 -> predictionFixtures.add(HalfTimeOneXTwoPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(TWO)
            .build());
        case PLAYER_SCORE -> predictionFixtures.add(PlayerScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .playerId(PLAYER_ID_FOR_LOST_PREDICTIONS)
            .build());
        case PENALTY_MATCH -> predictionFixtures.add(PenaltyMatchPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .build());
        case DOUBLE_CHANCE -> predictionFixtures.add(DoubleChancePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(DoubleChanceOutcome.DRAW_TWO)
            .build());
        case RED_CARD_MATCH -> predictionFixtures.add(RedCardMatchPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(false)
            .build());
        case PLAYER_RED_CARD -> predictionFixtures.add(PlayerRedCardPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .playerId(PLAYER_ID_FOR_LOST_PREDICTIONS)
            .build());
        case PLAYER_SCORE_TWICE -> predictionFixtures.add(
            PlayerScoreTwicePredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .playerId(PLAYER_ID_FOR_LOST_PREDICTIONS)
                .build());
        case PLAYER_SCORE_HATTRICK -> predictionFixtures.add(
            PlayerScoreHattrickPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .playerId(PLAYER_ID_FOR_LOST_PREDICTIONS)
                .build());
        case PLAYER_YELLOW_CARD -> predictionFixtures.add(
            PlayerYellowCardPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .playerId(PLAYER_ID_FOR_LOST_PREDICTIONS)
                .build());
        case PLAYER_SCORE_FIRST_GOAL -> predictionFixtures.add(
            PlayerScoreFirstGoalPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .playerId(PLAYER_ID_FOR_LOST_PREDICTIONS)
                .build());
      }
    });
  }

  /**
   * The method sets for each valid market correct or incorrect prediction (hard-coded) as per the preliminary set match statistics
   *
   * @param markets            - all valid market
   * @param match              - the match for which predictions will be set
   * @param predictionFixtures - all valid markets fixtures
   */
  public static void setPartiallyCorrectPredictionsForAllMarkets(List<PredictionMarket> markets,
      Match match,
      ArrayList<PredictionFixture> predictionFixtures) {
    markets.forEach(market -> {
      switch (market) {
        case CORNERS_MATCH -> predictionFixtures.add(CornersMatchPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners())
            .build());
        case CORRECT_SCORE -> predictionFixtures.add(CorrectScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsHome(match.getGoalsFullTimeHome() + 1)
            .goalsAway(match.getGoalsFullTimeAway())
            .build());
        case CORRECT_SCORE_HT -> predictionFixtures.add(CorrectScoreHTPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .goalsHome(match.getGoalsHalfTimeHome())
            .goalsAway(match.getGoalsHalfTimeAway())
            .build());
        case CORRECT_SCORE_ADVANCED ->
            predictionFixtures.add(CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(match.getGoalsFullTimeHome())
                .goalsAway(match.getGoalsFullTimeAway())
                .build());
        case FT_1X2 -> predictionFixtures.add(FullTimeOneXTwoPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(ONE)
            .build());

        case BOTH_TEAMS_SCORE -> predictionFixtures.add(BothTeamsScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .build());
        case HT_FT -> predictionFixtures.add(HalfTimeFullTimePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(ONE_ONE)
            .build());
        case OVER_GOALS_0_5 -> predictionFixtures.add(OverGoals_0_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() < 0.5f)
            .build());
        case OVER_GOALS_1_5 -> predictionFixtures.add(OverGoals_1_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 1.5f)
            .build());
        case OVER_GOALS_2_5 -> predictionFixtures.add(OverGoals_2_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 2.5f)
            .build());
        case OVER_GOALS_3_5 -> predictionFixtures.add(OverGoals_3_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 3.5f)
            .build());
        case OVER_GOALS_4_5 -> predictionFixtures.add(OverGoals_4_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 4.5f)
            .build());
        case OVER_GOALS_5_5 -> predictionFixtures.add(OverGoals_5_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 5.5f)
            .build());
        case OVER_GOALS_6_5 -> predictionFixtures.add(OverGoals_6_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getGoalsFullTimeHome() + match.getGoalsFullTimeAway() > 6.5f)
            .build());
        case HT_1X2 -> predictionFixtures.add(HalfTimeOneXTwoPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(TWO)
            .build());
        case OVER_CORNERS_6_5 -> predictionFixtures.add(OverCorners_6_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() > 6.5f)
            .build());
        case OVER_CORNERS_7_5 -> predictionFixtures.add(OverCorners_7_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() < 7.5f)
            .build());
        case OVER_CORNERS_8_5 -> predictionFixtures.add(OverCorners_8_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() > 8.5f)
            .build());
        case OVER_CORNERS_9_5 -> predictionFixtures.add(OverCorners_9_5_PredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                .get(1)
                .getStatistics()
                .getCorners() > 9.5f)
            .build());
        case OVER_CORNERS_10_5 ->
            predictionFixtures.add(OverCorners_10_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() > 10.5f)
                .build());
        case OVER_CORNERS_11_5 ->
            predictionFixtures.add(OverCorners_11_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() < 11.5f)
                .build());
        case OVER_CORNERS_12_5 ->
            predictionFixtures.add(OverCorners_12_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() > 12.5f)
                .build());
        case OVER_CORNERS_13_5 ->
            predictionFixtures.add(OverCorners_13_5_PredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(match.getStats().get(0).getStatistics().getCorners() + match.getStats()
                    .get(1)
                    .getStatistics()
                    .getCorners() > 13.5f)
                .build());
        case PLAYER_SCORE -> predictionFixtures.add(PlayerScorePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .playerId(VALID_PLAYER_ID)
            .build());
        case PENALTY_MATCH -> predictionFixtures.add(PenaltyMatchPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(false)
            .build());
        case DOUBLE_CHANCE -> predictionFixtures.add(DoubleChancePredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(DoubleChanceOutcome.ONE_DRAW)
            .build());
        case RED_CARD_MATCH -> predictionFixtures.add(RedCardMatchPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .build());
        case PLAYER_RED_CARD -> predictionFixtures.add(PlayerRedCardPredictionFixture.builder()
            .matchId(match.getId())
            .matchType(MatchType.FOOTBALL.getValue())
            .prediction(true)
            .playerId(VALID_PLAYER_ID)
            .build());
        case PLAYER_SCORE_TWICE -> predictionFixtures.add(
            PlayerScoreTwicePredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .playerId(VALID_PLAYER_ID)
                .build());
        case PLAYER_SCORE_HATTRICK -> predictionFixtures.add(
            PlayerScoreHattrickPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .playerId(VALID_PLAYER_ID)
                .build());
        case PLAYER_YELLOW_CARD -> predictionFixtures.add(
            PlayerYellowCardPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .prediction(true)
                .playerId(VALID_PLAYER_ID)
                .build());
        case PLAYER_SCORE_FIRST_GOAL -> predictionFixtures.add(
            PlayerScoreFirstGoalPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .playerId(VALID_PLAYER_ID)
                .prediction(true)
                .build());
      }
    });
  }

  public static Response getOwnPredictions(GameType gameTypes, PredictionStatus status)
      throws HttpException {

    return getOwnPredictions(gameTypes.getValue(), status.getValue(), null,
        FirebaseHelper.FansUnitedProject.FANS_UNITED_PROFILE, getCurrentTestUser().getEmail(),
        CLIENT_AUTOMATION_ID, AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON, -1, null);
  }

  /**
   * The method is used to create a prediction for Match Quiz Game with only one market fixture
   *
   * @param gameId           the id of the game for which the prediction will be made
   * @param predictionMarket the market with which a prediction fixture will be created
   * @return the id of the created prediction
   * @throws HttpException
   * @throws IllegalArgumentException
   */
  public static String createMatchQuizPredictionForGameWithSingleMarketAndSameUser(String gameId,
      PredictionMarket predictionMarket, String userEmail)
      throws HttpException, IllegalArgumentException {

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchId = gameObject.getFixtures().get(0).getMatchId();

    var playerId = getRandomPlayerFromMatch(matchId);

    List<PredictionFixture> predictionFixtures =
        generateValidSinglePredictionFixture(predictionMarket, matchId, playerId);

    var response = createPredictionForUser(CreatePredictionRequest.builder()
        .fixtures(predictionFixtures)
        .gameInstanceId(gameObject.getId())
        .build(), userEmail);

    var predictionId = response.then().extract().body().jsonPath().getString(ID_PROP);

    Assertions.assertNotNull(predictionId, "Prediction ID is NULL!");

    return predictionId;
  }

  public static String createMatchQuizPredictionForGameWithSingleMarketOwnGoal(String gameId,
      PredictionMarket predictionMarket, String userEmail, String playerId)
      throws HttpException, IllegalArgumentException {

    var createGameResponse = GameEndpoint.getGameById(gameId);

    var gameObject = createGameResponse.as(GameInstance.class);

    var matchId = gameObject.getFixtures().get(0).getMatchId();


    List<PredictionFixture> predictionFixtures =
        generateValidSinglePredictionFixture(predictionMarket, matchId, playerId);

    var response = createPredictionForUser(CreatePredictionRequest.builder()
        .fixtures(predictionFixtures)
        .gameInstanceId(gameObject.getId())
        .build(), userEmail);

    var predictionId = response.then().extract().body().jsonPath().getString(ID_PROP);

    Assertions.assertNotNull(predictionId, "Prediction ID is NULL!");

    return predictionId;
  }

  /**
   * The method creates a Fixture based on the type of Advanced Prediction
   * targeted outcome, and then appends it to a List
   *
   * @param advancedPrediction the type of targeted outcome
   * @param match              the match for teh fixture
   * @param predictionFixtures the list to which the fixture will be added
   */
  public static void createPredictionFixtureForAdvancedCorrectScore(
      AdvancedPredictions advancedPrediction, Match match,
      ArrayList<PredictionFixture> predictionFixtures) {
    switch (advancedPrediction) {
      case CORRECT_RESULT -> {
        predictionFixtures.add(
            CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(match.getGoalsFullTimeHome())
                .goalsAway(match.getGoalsFullTimeAway())
                .build());
      }
      case INCORRECT_RESULT -> {
        predictionFixtures.add(
            CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(3)
                .goalsAway(4)
                .build());
      }
      case CORRECT_HOME_TEAM -> {
        predictionFixtures.add(
            CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(4)
                .goalsAway(5)
                .build());
      }
      case CORRECT_HOME_TEAM_WITH_CORRECT_NEGATIVE_DIFF -> {
        predictionFixtures.add(
            CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(4)
                .goalsAway(6)
                .build());
      }
      case CORRECT_HOME_TEAM_AND_OUTCOME -> {
        predictionFixtures.add(
            CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(4)
                .goalsAway(3)
                .build());
      }
      case CORRECT_AWAY_TEAM -> {
        predictionFixtures.add(
            CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(1)
                .goalsAway(2)
                .build());
      }
      case CORRECT_AWAY_TEAM_WITH_CORRECT_NEGATIVE_DIFF -> {
        predictionFixtures.add(
            CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(0)
                .goalsAway(2)
                .build());
      }
      case CORRECT_AWAY_TEAM_AND_OUTCOME -> {
        predictionFixtures.add(
            CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(3)
                .goalsAway(2)
                .build());
      }
      case CORRECT_DIFF_AND_OUTCOME -> {
        match.setGoalsFullTimeHome((byte) 3);
        match.setGoalsFullTimeAway((byte) 1);
        predictionFixtures.add(
            CorrectScoreAdvancedPredictionFixture.builder()
                .matchId(match.getId())
                .matchType(MatchType.FOOTBALL.getValue())
                .goalsHome(3)
                .goalsAway(1)
                .build());
      }
    }
  }
}
