package com.fansunited.automation.core.apis.footballapi;

import static com.fansunited.automation.constants.UrlParamValues.FootballApi.PATH_PARAM_PLAYER_ID;
import static com.fansunited.automation.constants.UrlParamValues.ProfileApi.CLIENT_AUTOMATION_ID;
import static com.fansunited.automation.constants.UrlParamValues.QUERY_PARAM_CLIENT_ID;

import com.fansunited.automation.constants.AuthConstants;
import com.fansunited.automation.constants.Endpoints;
import com.fansunited.automation.constants.UrlParamValues;
import com.fansunited.automation.model.footballapi.players.Player;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import java.util.UUID;

public class PlayerByIdEndpoint extends BaseSetup {

  public static Player getPlayerDto(String playerId) {

    return getPlayerById(playerId).then()
        .extract()
        .body()
        .jsonPath()
        .getObject("data", Player.class);
  }

  public static Response getPlayerById(String playerId) {

    return getPlayerById(playerId, UrlParamValues.Language.EN.getValue(),
        AuthConstants.ENDPOINTS_API_KEY, ContentType.JSON);
  }

  public static Response getPlayerById(String playerId, String lang,
      String apiKey, ContentType contentType) {

    var requestSpecification =
        getRequiredRequestSpec(lang, null, apiKey, contentType);

    // Avoid cache
    requestSpecification =
        requestSpecification.queryParam("dummy", UUID.randomUUID().toString().replace("-", ""));

    return requestSpecification
        .pathParam(PATH_PARAM_PLAYER_ID, playerId)
        .when()
        .queryParam(QUERY_PARAM_CLIENT_ID,CLIENT_AUTOMATION_ID)
        .get(Endpoints.FootballApi.PLAYER_BY_ID);
  }
}
