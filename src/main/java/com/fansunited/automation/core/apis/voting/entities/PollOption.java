package com.fansunited.automation.core.apis.voting.entities;

import com.fansunited.automation.model.common.Images;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.javafaker.Faker;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class PollOption {
  private String id;
  private String title;
  private String description;
  private Images images;
  private int votes;
  private List<PollPreference> preferencesMapping;

  @JsonProperty("embed_code")
  private String embedCode;

  public static PollOption createOptionWithRandomData() {
    Faker faker = new Faker();
    String htmlCode =
        """
          <!DOCTYPE html>
          <html>
          <head>
            <title>Button Example</title>
          </head>
          <body>
           <p>%s</p>
            <button id="testButton" onclick="alert('Button clicked!')">click test</button>
          </body>
          </html>
         """
            .formatted(faker.chuckNorris().fact());

    return PollOption.builder()
        .title(faker.lorem().word())
        .description(faker.lorem().sentence())
        .images(Images.createImagesWithRandomData())
        .embedCode(htmlCode)
        .build();
  }

  public static PollOption createOptionWithPreferencesAndRandomData() {
    Faker faker = new Faker();
    var pollOption = createOptionWithRandomData();
    pollOption.setPreferencesMapping(
        List.of(
            new PollPreference(faker.company().name()),
            new PollPreference(faker.company().name())));
    return pollOption;
  }
}
