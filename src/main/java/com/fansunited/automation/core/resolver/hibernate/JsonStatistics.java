package com.fansunited.automation.core.resolver.hibernate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor @AllArgsConstructor
public class JsonStatistics {
  private Byte goals;
  private Byte corners;
  private Byte crosses;
  private Byte offside;
  private Byte shots_on;
  private Byte throw_in;
  private Byte red_cards;
  private Byte red_cards_1h;
  private Byte red_cards_2h;
  private Byte red_cards_et;
  private Byte shots_off;
  private Byte goal_kicks;
  private Byte possession;
  private Byte treatments;
  private Byte yellow_cards;
  private Byte yellow_cards_1h;
  private Byte yellow_cards_2h;
  private Byte shots_blocked;
  private Byte substitutions;
  private Byte counter_attacks;
  private Byte fouls_committed;
  private Byte corners_1h;
  private Byte corners_2h;
  private Byte corners_et;
}