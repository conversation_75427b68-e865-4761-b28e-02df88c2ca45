package com.fansunited.automation.core.resolver.hibernate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@ToString
@Builder
@Getter
@Setter
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "event_team_stats")
public class MatchStats {
  @Id
  private long id;

  @Column(name = "team_id")
  private String teamId;

  @Column(name = "event_id")
  private String eventId;

  @Column(name = "team_name")
  private String teamName;

  @Column(name = "home_team")
  private boolean homeTeam;

  @JdbcTypeCode(SqlTypes.JSON)
  @Column(columnDefinition = "json")
  private JsonStatistics statistics;

  @Column(name = "updated_at")
  private LocalDateTime updatedAt;
}