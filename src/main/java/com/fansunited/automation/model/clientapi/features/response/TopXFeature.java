package com.fansunited.automation.model.clientapi.features.response;

import com.fansunited.automation.constants.ApiConstants;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopXFeature {
  private Integer minFixtures;
  private Integer maxFixtures;
  private List<String> competitionsWhitelist;
  private boolean enabled;
  private String clientId;
  private ApiConstants.AuthRequirement authRequirement;
}
