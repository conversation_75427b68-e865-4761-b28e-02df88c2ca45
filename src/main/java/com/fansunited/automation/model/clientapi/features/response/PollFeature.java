package com.fansunited.automation.model.clientapi.features.response;

import com.fansunited.automation.constants.ApiConstants;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@AllArgsConstructor
@Jacksonized
@JsonIgnoreProperties(ignoreUnknown = true)
public class PollFeature {

    private String client_id;
    private boolean enabled;
    private String id;
    private List<Types> types;
    private ApiConstants.AuthRequirement authRequirement;
}
