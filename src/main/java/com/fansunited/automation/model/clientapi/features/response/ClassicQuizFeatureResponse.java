package com.fansunited.automation.model.clientapi.features.response;

import com.fansunited.automation.constants.ApiConstants;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.Data;

/**
 * Classic Quiz feature response.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class
ClassicQuizFeatureResponse {
  private ClassicQuizFeature data;
  private boolean enabled;
  private List<BasicObject> types;
  private String client_id;
  private ApiConstants.AuthRequirement authRequirement;
}
