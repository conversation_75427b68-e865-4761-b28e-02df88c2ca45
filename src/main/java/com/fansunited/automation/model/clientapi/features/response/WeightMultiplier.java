package com.fansunited.automation.model.clientapi.features.response;

import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.AddPostMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.AddReactionMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.ArticleConsumedMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.AudioConsumedMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.ClassicQuizParticipationMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.ClickAdMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.CommentMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.ContentConsumedMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.ConvertMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.DeletePostMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.DislikeMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.EitherOrParticipationMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.GalleryConsumedMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.GameParticipationMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.ImageConsumedMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.LikeMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.LongVideoConsumedMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.ManageInterestsMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.PageViewMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.PollParticipationMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.PredictionMadeMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.PrivateLeagueCreatMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.PrivateLeagueJoinMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.PrivateLeagueLeaveMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.ProfileUpdateMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.ShareMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.ShortVideoConsumedMultiplier;
import com.fansunited.automation.model.clientapi.features.response.DefaultMultipliers.VideoConsumedMultiplier;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class WeightMultiplier {
  private String id;
  private int weight;
  private int multiplier = 1;
  private List<String> conditions;

  public static List<WeightMultiplier> getDefaultLoyaltyWeightMultipliers() {

    List<WeightMultiplier> weightMultipliers = new ArrayList<>();

    weightMultipliers.add(ClickAdMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(CommentMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(ContentConsumedMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(ConvertMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(DislikeMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(GameParticipationMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(LikeMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(ManageInterestsMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(PageViewMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(PredictionMadeMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(ProfileUpdateMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(ShareMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(LongVideoConsumedMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(ShortVideoConsumedMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(VideoConsumedMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(PollParticipationMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(ImageConsumedMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(AudioConsumedMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(GalleryConsumedMultiplier.returnAsWeightMultiplier());
    // New multipliers
    weightMultipliers.add(AddPostMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(AddReactionMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(ArticleConsumedMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(ClassicQuizParticipationMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(DeletePostMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(EitherOrParticipationMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(PrivateLeagueCreatMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(PrivateLeagueJoinMultiplier.returnAsWeightMultiplier());
    weightMultipliers.add(PrivateLeagueLeaveMultiplier.returnAsWeightMultiplier());

    return weightMultipliers;
  }
}
