package com.fansunited.automation.model.clientapi.features.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Jacksonized
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeaturesResponse {
 private Features data;
 private ExternalPoints externalPoints;
 private Object error;
}
