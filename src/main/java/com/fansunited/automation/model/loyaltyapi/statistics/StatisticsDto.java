package com.fansunited.automation.model.loyaltyapi.statistics;

import com.fansunited.automation.model.loyaltyapi.statistics.predictions.Predictions;
import java.util.List;
import lombok.Data;

@Data
public class StatisticsDto {
  private String profileId;
  private String tier;
  private int points;
  private int predictionsMade;
  private SuccessRateDto successRates;
  private Predictions predictions;
  private List<AchievedBadgesDto> badges;
  private List<AchievedTiersDto> tiers;
  private DiscussionsDto discussions;
  private ClassicQuizzesDto classicQuizzes;
  private EitherOrDto eitherOr;
  private VotingDto voting;
}
