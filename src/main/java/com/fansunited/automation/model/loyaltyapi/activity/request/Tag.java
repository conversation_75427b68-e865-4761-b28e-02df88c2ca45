package com.fansunited.automation.model.loyaltyapi.activity.request;

import com.github.javafaker.Faker;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@AllArgsConstructor
@Jacksonized
public class Tag {
  private String id;
  private String type;
  private String source;

  public static  Tag createTagRequestWithRandomData() {
    var faker = new Faker();
    return  Tag.builder()
            .id(faker.idNumber().valid())
            .type(faker.file().mimeType())
            .source("football")
            .build();
  }
}