package com.fansunited.automation.model.minigamesapi.classicquiz.request;

import com.fansunited.automation.model.minigamesapi.classicquiz.BrandingDTO;
import com.fansunited.automation.model.minigamesapi.classicquiz.GameImagesDto;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ClassicQuizQuestionsUpdateRequest {

  private int questionId;
  private String question;
  private GameImagesDto images;
  private List<ClassicQuizOptionsUpdateRequest> options;
  private String embed_code;
  private BrandingDTO branding;
  private String explanation;
}