package com.fansunited.automation.model.reportingapi.users.registeredusers;

import com.fansunited.automation.model.reportingapi.users.common.TotalByGenderDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import lombok.Data;

@Data
public class DateBreakdownDto {
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "UTC")
  private LocalDate date;
  private TotalByGenderDto users;
}
