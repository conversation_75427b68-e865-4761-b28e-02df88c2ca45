package com.fansunited.automation.model.profileapi.leades;

import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class LeadDto {
    private String leadId;
    private String profileId;
    private String fullName;
    private String firstName;
    private String lastName;
    private String email;
    private String gender;
    private String country;
    private String phoneCountryCode;
    private String phoneNumber;
    private String campaignId;
    private String campaignName;
    private String contentType;
    private String contentId;
    private String contentName;
    private Map<String, String> customFields;
    private String createdAt;
}