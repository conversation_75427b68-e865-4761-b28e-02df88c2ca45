package com.fansunited.automation.model.predictionapi.games.predictionfixtures.enums;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Arrays;
import java.util.Random;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum DoubleChanceOutcome {
  @JsonProperty("1x")
  ONE_DRAW("1x"),

  @JsonProperty("x2")
  DRAW_TWO("x2"),

  @JsonProperty("12")
  ONE_TWO("12"),

  @JsonProperty("1")
  INVALID("1");

  @Getter
  private final String value;

  public static DoubleChanceOutcome getRandomDoubleChanceOutcome() {
    var validOutcomes =
        Arrays.stream(values()).filter(value -> value != DoubleChanceOutcome.INVALID).toList();
    var random = new Random();
    return validOutcomes.get(random.nextInt(validOutcomes.size()));
  }
}
