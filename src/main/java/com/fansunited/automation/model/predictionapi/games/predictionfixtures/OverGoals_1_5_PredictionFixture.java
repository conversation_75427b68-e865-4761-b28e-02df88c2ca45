package com.fansunited.automation.model.predictionapi.games.predictionfixtures;

import com.fansunited.automation.model.predictionapi.games.enums.PredictionMarket;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class OverGoals_1_5_PredictionFixture extends PredictionFixture {
  private final PredictionMarket market = PredictionMarket.OVER_GOALS_1_5;

  @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
  private boolean prediction;
  @JsonIgnore
  private boolean ignorePrediction;

  @JsonAnyGetter
  public Map<String, Object> getAdditionalProperties() {
    var props = super.getProperties();
    if (!ignorePrediction) {
      props.put("prediction", prediction);
    }
    return props;
  }
}
