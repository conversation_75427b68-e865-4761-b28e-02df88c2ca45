package com.fansunited.automation.model.footballapi.matches;

import com.fansunited.automation.core.resolver.hibernate.PlayerMatchStats;
import com.fansunited.automation.helpers.DateFormatter;
import com.fansunited.automation.model.footballapi.matches.scores.Scores;
import com.fansunited.automation.model.footballapi.matches.stats.Stats;
import com.fansunited.automation.model.footballapi.teams.Team;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class Match {
  private String id;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime kickoffAt;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime finishedAt;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime updatedAt;
  private Team homeTeam;
  private Team awayTeam;
  private Boolean lineupsConfirmed;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO8601_PATTERN)
  private LocalDateTime startedAt;
  private Integer minute;
  private Scores scores;
  private Stats stats;
  private Context context;
  private List<MatchTimeline> timeline;
  private MatchStatus status;
  private MatchLineups lineups;
  private Boolean is_deleted;
  private Boolean undecided;
  private List<PlayerMatchStats> player_stats;
}
