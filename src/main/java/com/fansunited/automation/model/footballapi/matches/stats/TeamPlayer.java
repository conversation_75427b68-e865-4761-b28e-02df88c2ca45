package com.fansunited.automation.model.footballapi.matches.stats;

import com.fansunited.automation.helpers.DateFormatter;
import com.fansunited.automation.model.footballapi.common.Country;
import com.fansunited.automation.model.footballapi.players.AssetsHeadshot;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import lombok.Data;

@Data
public class TeamPlayer {
  private String id;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO_DATE)
  private LocalDate startDate;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO_DATE)
  private LocalDate endDate;
  private Integer shirtNumber;
  private boolean loan;
  private Country country;
  private String name;
  private AssetsHeadshot assets;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DateFormatter.ISO_DATE)
  private String birthDate;
  private String firstName;
  private String lastName;
  private String position;
}
